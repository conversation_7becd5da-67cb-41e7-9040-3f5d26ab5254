/**
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
 */
/**
 * @module ui/dropdown/button/dropdownbuttonview
 */
import ButtonView from '../../button/buttonview';
import type DropdownButton from './dropdownbutton';
import IconView from '../../icon/iconview';
import type { Locale } from '@ckeditor/ckeditor5-utils';
/**
 * The default dropdown button view class.
 *
 * ```ts
 * const view = new DropdownButtonView();
 *
 * view.set( {
 * 	label: 'A button',
 * 	keystroke: 'Ctrl+B',
 * 	tooltip: true
 * } );
 *
 * view.render();
 *
 * document.body.append( view.element );
 * ```
 *
 * Also see the {@link module:ui/dropdown/utils~createDropdown `createDropdown()` util}.
 */
export default class DropdownButtonView extends ButtonView implements DropdownButton {
    /**
     * An icon that displays arrow to indicate a dropdown button.
     */
    readonly arrowView: IconView;
    /**
     * @inheritDoc
     */
    constructor(locale?: Locale);
    /**
     * @inheritDoc
     */
    render(): void;
    /**
     * Creates a {@link module:ui/icon/iconview~IconView} instance as {@link #arrowView}.
     */
    private _createArrowView;
}
