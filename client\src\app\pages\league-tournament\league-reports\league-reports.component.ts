import {
  Component,
  OnInit
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-league-reports',
  templateUrl: './league-reports.component.html',
  styleUrls: ['./league-reports.component.scss'],
})
export class LeagueReportsComponent implements OnInit
{
  public contentHeader: object;

  constructor(
    public _trans: TranslateService,
    public _titleService: Title
  ) {
    this._titleService.setTitle('Matches Report');
  }

  ngOnInit(): void {
    this.contentHeader = {
      headerTitle: this._trans.instant('Matches Report'),
      actionButton: false,
      breadcrumb: {
        type: '',
        links: [
          {
            name: this._trans.instant('Home'),
            isLink: false
          },
          {
            name: this._trans.instant('Tournaments'),
            isLink: false
          },
          {
            name: this._trans.instant('Matches Report'),
            isLink: false
          }
        ]
      }
    };
  }
}
