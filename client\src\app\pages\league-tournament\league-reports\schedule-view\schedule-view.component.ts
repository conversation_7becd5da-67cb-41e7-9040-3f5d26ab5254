import {
  Component,
  EventEmitter,
  OnInit,
  AfterViewInit,
  OnDestroy
} from '@angular/core';
import { SeasonService } from 'app/services/season.service';
import { TournamentService } from 'app/services/tournament.service';
import moment from 'moment';
import { LoadingService } from 'app/services/loading.service';
import { coreConfig } from '../../../../app-config';
import { CommonsService } from 'app/services/commons.service';
import { AppConfig } from 'app/app-config';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-schedule-view',
  templateUrl: './schedule-view.component.html',
  styleUrls: ['./schedule-view.component.scss']
})
export class ScheduleViewComponent implements OnInit, AfterViewInit, OnDestroy {
  onSelectTab = new EventEmitter();
  activeIds = '';
  selectedTabIndex = 0;
  tabs = [];
  matches: any = {};
  tab: any;
  selectedTeam: any;
  selectedTournament: any;
  seasons = [];
  teams = [];
  tournaments = [];
  coreConfig = coreConfig;
  params = { season_id: null, tournament_id: null };
  hasSeason = true;
  AppConfig = AppConfig;

  constructor(
    public _tourService: TournamentService,
    public _seasonService: SeasonService,
    public _loadingService: LoadingService,
    public _commonsService: CommonsService,
    public _translateService: TranslateService
  ) {
    // Initialize with default values - don't use router to avoid conflicts
    this.params.tournament_id = null;
    this.params.season_id = null;
  }

  ngOnInit(): void {
    this.getSeasons();
  }

  ngAfterViewInit(): void {
    // Implementation for after view init
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  getSeasons() {
    this._seasonService
      .getCurrentSeason('matches')
      .toPromise()
      .then((res) => {
        this.seasons = res;
        if (this.seasons.length == 0) {
          this._loadingService.dismiss();
          this.hasSeason = false;
          return;
        }
        this.params.season_id = this.params.season_id
          ? this.params.season_id
          : res[0].id;
        this.getTournamentOptions(this.params.season_id);
      });
  }

  getTournamentOptions(season_id: any) {
    this._seasonService
      .getTournamentOptions(season_id, 0, true)
      .subscribe((res) => {
        this.tournaments = res.tournaments;
        if (this.tournaments.length == 0) {
          this.tabs = [];
        }

        this.params.tournament_id =
          this.params.tournament_id || res.tournaments[0]?.id || null;
        this.selectedTournament = this.tournaments.find(
          (tournament) => tournament.id === this.params.tournament_id
        );
        this.onSelectTournament(this.selectedTournament);
      });
  }

  onSelectTournament(tournament: any) {
    this.selectedTournament = tournament;
    this.params.tournament_id = tournament?.id;
    // Remove router navigation to avoid conflicts with Table View
    this.getMatchesWithQuery();
  }

  getMatchesWithQuery() {
    this._tourService
      .getMatchesWithQuery(
        this.params.season_id,
        `?tournament_id=${this.selectedTournament?.id}`
      )
      .subscribe(
        (res) => {
          let today = moment().format('YYYY-MM-DD');
          this.matches = res.matches;
          let numberOfMatches = Object.keys(this.matches).length;
          let tabs = [];
          if (numberOfMatches == 0) {
            this.tabs = tabs;
            return;
          }
          let count = 0;
          this.activeIds = '';
          for (let key in this.matches) {
            if (key) {
              this.activeIds += `match-panel-${count},`;
            }
            count++;
            let index = tabs.length;
            let label = 'TBD';
            let date = 'TBD';
            if (key) {
              label = moment(key).format('ddd DD MMM');
              date = moment(key).format('YYYY-MM-DD');
            }

            if (date == today) {
              label = 'Today';
            }
            if (label != 'TBD') {
              tabs.push({
                label: label,
                value: date,
                index: index
              });
            }
          }

          if (this.matches) {
            // select tab has date nearest to today
            let nearestDate = Object.keys(this.matches).reduce((a, b) => {
              return Math.abs(moment(a).diff(today, 'days')) <
                Math.abs(moment(b).diff(today, 'days'))
                ? a
                : b;
            });

            this.selectedTabIndex = tabs.findIndex(
              (tab) => tab.value === moment(nearestDate).format('YYYY-MM-DD')
            );

            // sort tabs by date
            tabs.sort((a, b) => {
              return moment(a.value).diff(moment(b.value));
            });
            this.tabs = tabs;
            setTimeout(() => {
              this.onSelectTab.emit(this.selectedTabIndex);
            }, 500);
          }
        },
        (error) => {
          console.error('Error loading matches:', error);
        },
        () => {
          this._loadingService.dismiss();
        }
      );
  }

  selectedTab(tab: any) {
    this.tab = tab;
    let element = document.getElementById(tab.value);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  onSelectMatch(match: any) {
    // Handle match selection if needed
    console.log('Selected match:', match);
  }



  onTournamentChange(tournamentId: any) {
    const tournament = this.tournaments.find((t: any) => t.id === tournamentId);
    this.onSelectTournament(tournament);
  }
}
