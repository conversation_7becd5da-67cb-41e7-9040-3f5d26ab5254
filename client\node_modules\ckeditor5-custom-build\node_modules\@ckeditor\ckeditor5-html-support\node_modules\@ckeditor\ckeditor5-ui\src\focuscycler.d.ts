/**
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
 */
/**
 * @module ui/focuscycler
 */
import { type ArrayOrItem, type FocusTracker, type KeystrokeHandler } from '@ckeditor/ckeditor5-utils';
import type View from './view';
import type ViewCollection from './viewcollection';
/**
 * A utility class that helps cycling over focusable {@link module:ui/view~View views} in a
 * {@link module:ui/viewcollection~ViewCollection} when the focus is tracked by the
 * {@link module:utils/focustracker~FocusTracker} instance. It helps implementing keyboard
 * navigation in HTML forms, toolbars, lists and the like.
 *
 * To work properly it requires:
 * * a collection of focusable (HTML `tabindex` attribute) views that implement the `focus()` method,
 * * an associated focus tracker to determine which view is focused.
 *
 * A simple cycler setup can look like this:
 *
 * ```ts
 * const focusables = new ViewCollection();
 * const focusTracker = new FocusTracker();
 *
 * // Add focusable views to the focus tracker.
 * focusTracker.add( ... );
 * ```
 *
 * Then, the cycler can be used manually:
 *
 * ```ts
 * const cycler = new FocusCycler( { focusables, focusTracker } );
 *
 * // Will focus the first focusable view in #focusables.
 * cycler.focusFirst();
 *
 * // Will log the next focusable item in #focusables.
 * console.log( cycler.next );
 * ```
 *
 * Alternatively, it can work side by side with the {@link module:utils/keystrokehandler~KeystrokeHandler}:
 *
 * ```ts
 * const keystrokeHandler = new KeystrokeHandler();
 *
 * // Activate the keystroke handler.
 * keystrokeHandler.listenTo( sourceOfEvents );
 *
 * const cycler = new FocusCycler( {
 * 	focusables, focusTracker, keystrokeHandler,
 * 	actions: {
 * 		// When arrowup of arrowleft is detected by the #keystrokeHandler,
 * 		// focusPrevious() will be called on the cycler.
 * 		focusPrevious: [ 'arrowup', 'arrowleft' ],
 * 	}
 * } );
 * ```
 *
 * Check out the {@glink framework/deep-dive/ui/focus-tracking "Deep dive into focus tracking"} guide to learn more.
 */
export default class FocusCycler {
    /**
     * A {@link module:ui/view~View view} collection that the cycler operates on.
     */
    readonly focusables: ViewCollection;
    /**
     * A focus tracker instance that the cycler uses to determine the current focus
     * state in {@link #focusables}.
     */
    readonly focusTracker: FocusTracker;
    /**
     * An instance of the {@link module:utils/keystrokehandler~KeystrokeHandler}
     * which can respond to certain keystrokes and cycle the focus.
     */
    readonly keystrokeHandler?: KeystrokeHandler;
    /**
     * Actions that the cycler can take when a keystroke is pressed. Requires
     * `options.keystrokeHandler` to be passed and working. When an action is
     * performed, `preventDefault` and `stopPropagation` will be called on the event
     * the keystroke fired in the DOM.
     *
     * ```ts
     * actions: {
     * 	// Will call #focusPrevious() when arrowleft or arrowup is pressed.
     * 	focusPrevious: [ 'arrowleft', 'arrowup' ],
     *
     * 	// Will call #focusNext() when arrowdown is pressed.
     * 	focusNext: 'arrowdown'
     * }
     * ```
     */
    readonly actions?: FocusCyclerActions;
    /**
     * Creates an instance of the focus cycler utility.
     *
     * @param options Configuration options.
     */
    constructor(options: {
        focusables: ViewCollection;
        focusTracker: FocusTracker;
        keystrokeHandler?: KeystrokeHandler;
        actions?: FocusCyclerActions;
    });
    /**
     * Returns the first focusable view in {@link #focusables}.
     * Returns `null` if there is none.
     *
     * **Note**: Hidden views (e.g. with `display: none`) are ignored.
     */
    get first(): FocusableView | null;
    /**
     * Returns the last focusable view in {@link #focusables}.
     * Returns `null` if there is none.
     *
     * **Note**: Hidden views (e.g. with `display: none`) are ignored.
     */
    get last(): FocusableView | null;
    /**
     * Returns the next focusable view in {@link #focusables} based on {@link #current}.
     * Returns `null` if there is none.
     *
     * **Note**: Hidden views (e.g. with `display: none`) are ignored.
     */
    get next(): FocusableView | null;
    /**
     * Returns the previous focusable view in {@link #focusables} based on {@link #current}.
     * Returns `null` if there is none.
     *
     * **Note**: Hidden views (e.g. with `display: none`) are ignored.
     */
    get previous(): FocusableView | null;
    /**
     * An index of the view in the {@link #focusables} which is focused according
     * to {@link #focusTracker}. Returns `null` when there is no such view.
     */
    get current(): number | null;
    /**
     * Focuses the {@link #first} item in {@link #focusables}.
     *
     * **Note**: Hidden views (e.g. with `display: none`) are ignored.
     */
    focusFirst(): void;
    /**
     * Focuses the {@link #last} item in {@link #focusables}.
     *
     * **Note**: Hidden views (e.g. with `display: none`) are ignored.
     */
    focusLast(): void;
    /**
     * Focuses the {@link #next} item in {@link #focusables}.
     *
     * **Note**: Hidden views (e.g. with `display: none`) are ignored.
     */
    focusNext(): void;
    /**
     * Focuses the {@link #previous} item in {@link #focusables}.
     *
     * **Note**: Hidden views (e.g. with `display: none`) are ignored.
     */
    focusPrevious(): void;
    /**
     * Focuses the given view if it exists.
     */
    private _focus;
    /**
     * Returns the next or previous focusable view in {@link #focusables} with respect
     * to {@link #current}.
     *
     * @param step Either `1` for checking forward from {@link #current} or `-1` for checking backwards.
     */
    private _getFocusableItem;
}
export type FocusableView = View & {
    focus(): void;
};
export interface FocusCyclerActions {
    focusFirst?: ArrayOrItem<string>;
    focusLast?: ArrayOrItem<string>;
    focusNext?: ArrayOrItem<string>;
    focusPrevious?: ArrayOrItem<string>;
}
