{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nlet LeagueReportsComponent = class LeagueReportsComponent {\n  constructor(route, _router, _http, _trans, renderer, _loadingService, _registrationService, _clubService, _translateService, _titleService, calendar, formatter) {\n    this.route = route;\n    this._router = _router;\n    this._http = _http;\n    this._trans = _trans;\n    this.renderer = renderer;\n    this._loadingService = _loadingService;\n    this._registrationService = _registrationService;\n    this._clubService = _clubService;\n    this._translateService = _translateService;\n    this._titleService = _titleService;\n    this.calendar = calendar;\n    this.formatter = formatter;\n    this._titleService.setTitle('Matches Report');\n  }\n  _getCurrentSeason() {\n    this._loadingService.show();\n    this._registrationService.getAllSeasonActive().subscribe(data => {\n      this.seasons = data;\n      this.seasonId = this.seasons[0].id;\n      this._getTournaments(); // Fetch tournaments after getting seasons\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    }, () => {\n      this._loadingService.dismiss();\n    });\n  }\n  _getClubs() {\n    this._clubService.getAllClubs().subscribe(res => {\n      this.clubs = res.data;\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  onSelectSeason($event) {\n    return new Promise((resolve, reject) => {\n      this.seasonId = $event;\n      this._getTournaments(); // Fetch tournaments when season changes\n      // Reset tournament selection when season changes\n      this.tournamentId = null;\n      this.refreshChildDataTable();\n      resolve(true);\n    });\n  }\n  onSelectClub($event) {\n    console.log(`onSelectClub: ${$event}`);\n    this.clubId = $event;\n    this.refreshChildDataTable();\n  }\n  onSelectTournament($event) {\n    console.log(`onSelectTournament: ${$event}`);\n    this.tournamentId = $event;\n    this.refreshChildDataTable();\n  }\n  onSelectMatchStatus($event) {\n    console.log(`onSelectMatchStatus: ${$event}`);\n    this.matchStatus = $event;\n    this.refreshChildDataTable();\n  }\n  onDateSelection(date) {\n    if (!this.fromDate && !this.toDate) {\n      console.log('Setting From Date');\n      this.fromDate = date;\n      this.isSelecting = true;\n    } else if (this.fromDate && !this.toDate && date && date.after(this.fromDate)) {\n      this.toDate = date;\n      this.isSelecting = false;\n      this.updateDateRange();\n      setTimeout(() => {\n        if (this.dateRangePicker && this.dateRangePicker.close) {\n          this.dateRangePicker.close();\n        }\n      }, 0);\n    } else {\n      console.log('reset date form');\n      this.toDate = null;\n      this.fromDate = date;\n      this.isSelecting = true;\n    }\n  }\n  openDatePicker() {\n    this.isSelecting = false; // Reset selection state when opening\n    if (this.dateRangePicker) {\n      this.dateRangePicker.open();\n    }\n  }\n  onDocumentClick(event) {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n    this.clickOutsideTimeout = setTimeout(() => {\n      this.handleClickOutside(event);\n    }, 50);\n  }\n  handleClickOutside(event) {\n    // Check if datepicker is open\n    if (!this.dateRangePicker || !this.dateRangePicker.isOpen || !this.dateRangePicker.isOpen()) {\n      return;\n    }\n    if (this.isSelecting) {\n      return;\n    }\n    const target = event.target;\n    if (!target) return;\n    if (target.closest('.btn') || target.classList.contains('feather') || target.classList.contains('icon-calendar')) {\n      return;\n    }\n    if (target.tagName === 'INPUT' && target.getAttribute('name') === 'daterange') {\n      return;\n    }\n    const datepickerElement = document.querySelector('ngb-datepicker');\n    if (datepickerElement && datepickerElement.contains(target)) {\n      return;\n    }\n    this.dateRangePicker.close();\n  }\n  clearDateRange() {\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false;\n    this.updateDateRange();\n  }\n  validateInput(currentValue, input) {\n    const parsed = this.formatter.parse(input);\n    return parsed && this.calendar.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;\n  }\n  isHovered(date) {\n    return this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate);\n  }\n  isInside(date) {\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\n  }\n  isRange(date) {\n    return date.equals(this.fromDate) || this.toDate && date.equals(this.toDate) || this.isInside(date) || this.isHovered(date);\n  }\n  onDateRangeChange() {\n    console.log('onDateRangeChange:', this.dateRange);\n    this.refreshChildDataTable();\n  }\n  refreshChildDataTable() {\n    // Refresh the league table view component's DataTable\n    if (this.leagueTableViewComponent) {\n      this.leagueTableViewComponent.refreshDataTable();\n    }\n  }\n  updateDateRange() {\n    if (this.fromDate && this.toDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      const toMoment = moment(`${this.toDate.year}-${this.toDate.month.toString().padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\n      this.dateRangeValue = `${fromMoment.format('MMM DD, YYYY')} - ${toMoment.format('MMM DD, YYYY')}`;\n      this.onDateRangeChange();\n    } else if (this.fromDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = null;\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\n    } else {\n      this.dateRange.start_date = null;\n      this.dateRange.end_date = null;\n      this.dateRangeValue = '';\n      this.onDateRangeChange();\n    }\n  }\n  _getTournaments() {\n    if (this.seasonId) {\n      this._http.get(`${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`).subscribe(data => {\n        this.tournaments = data.data;\n      }, error => {\n        Swal.fire({\n          title: 'Error',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      });\n    }\n  }\n  ngOnInit() {\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Matches Report'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Home'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Tournaments'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Matches Report'),\n          isLink: false\n        }]\n      }\n    };\n    this._getCurrentSeason();\n    this._getClubs();\n  }\n  ngOnDestroy() {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n  }\n};\n__decorate([HostListener('document:click', ['$event'])], LeagueReportsComponent.prototype, \"onDocumentClick\", null);\nLeagueReportsComponent = __decorate([Component({\n  selector: 'app-league-reports',\n  templateUrl: './league-reports.component.html',\n  styleUrls: ['./league-reports.component.scss']\n})], LeagueReportsComponent);\nexport { LeagueReportsComponent };", "map": {"version": 3, "mappings": ";AAAA,SACEA,SAAS,QAEJ,eAAe;AASf,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAIjCC,YACUC,KAAqB,EACtBC,OAAe,EACfC,KAAiB,EACjBC,MAAwB,EACxBC,QAAmB,EACnBC,eAA+B,EAC/BC,oBAAyC,EACzCC,YAAyB,EACzBC,iBAAmC,EACnCC,aAAoB,EACnBC,QAAqB,EACtBC,SAAiC;IAXhC,KAAAX,KAAK,GAALA,KAAK;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,QAAQ,GAARA,QAAQ;IACT,KAAAC,SAAS,GAATA,SAAS;IAEhB,IAAI,CAACF,aAAa,CAACG,QAAQ,CAAC,gBAAgB,CAAC;EAC/C;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACR,eAAe,CAACS,IAAI,EAAE;IAC3B,IAAI,CAACR,oBAAoB,CAACS,kBAAkB,EAAE,CAACC,SAAS,CACrDC,IAAI,IAAI;MACP,IAAI,CAACC,OAAO,GAAGD,IAAI;MACnB,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACD,OAAO,CAAC,CAAC,CAAC,CAACE,EAAE;MAClC,IAAI,CAACC,eAAe,EAAE,CAAC,CAAC;IAC1B,CAAC,EACAC,KAAK,IAAI;MACRC,IAAI,CAACC,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEJ,KAAK,CAACK,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACrB,iBAAiB,CAACsB,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,EACD,MAAK;MACH,IAAI,CAACzB,eAAe,CAAC0B,OAAO,EAAE;IAChC,CAAC,CACF;EACH;EAEAC,SAASA,CAAA;IACP,IAAI,CAACzB,YAAY,CAAC0B,WAAW,EAAE,CAACjB,SAAS,CACtCkB,GAAG,IAAI;MACN,IAAI,CAACC,KAAK,GAAGD,GAAG,CAACjB,IAAI;IACvB,CAAC,EACAK,KAAK,IAAI;MACRC,IAAI,CAACC,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEJ,KAAK,CAACK,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACrB,iBAAiB,CAACsB,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEAM,cAAcA,CAACC,MAAW;IACxB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAACrB,QAAQ,GAAGkB,MAAM;MACtB,IAAI,CAAChB,eAAe,EAAE,CAAC,CAAC;MACxB;MACA,IAAI,CAACoB,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,qBAAqB,EAAE;MAC5BH,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;EACJ;EAEAI,YAAYA,CAACN,MAAW;IACtBO,OAAO,CAACC,GAAG,CAAC,iBAAiBR,MAAM,EAAE,CAAC;IACtC,IAAI,CAACS,MAAM,GAAGT,MAAM;IACpB,IAAI,CAACK,qBAAqB,EAAE;EAC9B;EAEAK,kBAAkBA,CAACV,MAAW;IAC5BO,OAAO,CAACC,GAAG,CAAC,uBAAuBR,MAAM,EAAE,CAAC;IAC5C,IAAI,CAACI,YAAY,GAAGJ,MAAM;IAC1B,IAAI,CAACK,qBAAqB,EAAE;EAC9B;EAEAM,mBAAmBA,CAACX,MAAW;IAC7BO,OAAO,CAACC,GAAG,CAAC,wBAAwBR,MAAM,EAAE,CAAC;IAC7C,IAAI,CAACY,WAAW,GAAGZ,MAAM;IACzB,IAAI,CAACK,qBAAqB,EAAE;EAC9B;EAEAQ,eAAeA,CAACC,IAAa;IAE3B,IAAI,CAAC,IAAI,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MAClCT,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,IAAI,CAACO,QAAQ,GAAGD,IAAI;MACpB,IAAI,CAACG,WAAW,GAAG,IAAI;KACxB,MAAM,IACL,IAAI,CAACF,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZF,IAAI,IACJA,IAAI,CAACI,KAAK,CAAC,IAAI,CAACH,QAAQ,CAAC,EACzB;MACA,IAAI,CAACC,MAAM,GAAGF,IAAI;MAClB,IAAI,CAACG,WAAW,GAAG,KAAK;MACxB,IAAI,CAACE,eAAe,EAAE;MACtBC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACC,eAAe,IAAI,IAAI,CAACA,eAAe,CAACC,KAAK,EAAE;UACtD,IAAI,CAACD,eAAe,CAACC,KAAK,EAAE;;MAEhC,CAAC,EAAE,CAAC,CAAC;KACN,MAAM;MACLf,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B,IAAI,CAACQ,MAAM,GAAG,IAAI;MAClB,IAAI,CAACD,QAAQ,GAAGD,IAAI;MACpB,IAAI,CAACG,WAAW,GAAG,IAAI;;EAE3B;EAEAM,cAAcA,CAAA;IACZ,IAAI,CAACN,WAAW,GAAG,KAAK,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACI,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACG,IAAI,EAAE;;EAE/B;EAGAC,eAAeA,CAACC,KAAY;IAC1B,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;IAGxC,IAAI,CAACA,mBAAmB,GAAGP,UAAU,CAAC,MAAK;MACzC,IAAI,CAACS,kBAAkB,CAACH,KAAK,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC;EACR;EAEQG,kBAAkBA,CAACH,KAAY;IACrC;IACA,IACE,CAAC,IAAI,CAACL,eAAe,IACrB,CAAC,IAAI,CAACA,eAAe,CAACS,MAAM,IAC5B,CAAC,IAAI,CAACT,eAAe,CAACS,MAAM,EAAE,EAC9B;MACA;;IAGF,IAAI,IAAI,CAACb,WAAW,EAAE;MACpB;;IAGF,MAAMc,MAAM,GAAGL,KAAK,CAACK,MAAqB;IAE1C,IAAI,CAACA,MAAM,EAAE;IAEb,IACEA,MAAM,CAACC,OAAO,CAAC,MAAM,CAAC,IACtBD,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,SAAS,CAAC,IACpCH,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,EAC1C;MACA;;IAGF,IACEH,MAAM,CAACI,OAAO,KAAK,OAAO,IAC1BJ,MAAM,CAACK,YAAY,CAAC,MAAM,CAAC,KAAK,WAAW,EAC3C;MACA;;IAGF,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;IAClE,IAAIF,iBAAiB,IAAIA,iBAAiB,CAACH,QAAQ,CAACH,MAAM,CAAC,EAAE;MAC3D;;IAGF,IAAI,CAACV,eAAe,CAACC,KAAK,EAAE;EAC9B;EAEAkB,cAAcA,CAAA;IACZ,IAAI,CAACzB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACE,eAAe,EAAE;EACxB;EAEAsB,aAAaA,CAACC,YAA4B,EAAEC,KAAa;IACvD,MAAMC,MAAM,GAAG,IAAI,CAACtE,SAAS,CAACuE,KAAK,CAACF,KAAK,CAAC;IAC1C,OAAOC,MAAM,IAAI,IAAI,CAACvE,QAAQ,CAACyE,OAAO,CAACC,OAAO,CAACC,IAAI,CAACJ,MAAM,CAAC,CAAC,GACxDG,OAAO,CAACC,IAAI,CAACJ,MAAM,CAAC,GACpBF,YAAY;EAClB;EAEAO,SAASA,CAACnC,IAAa;IACrB,OACE,IAAI,CAACC,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZ,IAAI,CAACkC,WAAW,IAChBpC,IAAI,CAACI,KAAK,CAAC,IAAI,CAACH,QAAQ,CAAC,IACzBD,IAAI,CAACqC,MAAM,CAAC,IAAI,CAACD,WAAW,CAAC;EAEjC;EAEAE,QAAQA,CAACtC,IAAa;IACpB,OAAO,IAAI,CAACE,MAAM,IAAIF,IAAI,CAACI,KAAK,CAAC,IAAI,CAACH,QAAQ,CAAC,IAAID,IAAI,CAACqC,MAAM,CAAC,IAAI,CAACnC,MAAM,CAAC;EAC7E;EAEAqC,OAAOA,CAACvC,IAAa;IACnB,OACEA,IAAI,CAACwC,MAAM,CAAC,IAAI,CAACvC,QAAQ,CAAC,IACzB,IAAI,CAACC,MAAM,IAAIF,IAAI,CAACwC,MAAM,CAAC,IAAI,CAACtC,MAAM,CAAE,IACzC,IAAI,CAACoC,QAAQ,CAACtC,IAAI,CAAC,IACnB,IAAI,CAACmC,SAAS,CAACnC,IAAI,CAAC;EAExB;EAEAyC,iBAAiBA,CAAA;IACfhD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACgD,SAAS,CAAC;IACjD,IAAI,CAACnD,qBAAqB,EAAE;EAC9B;EAEQA,qBAAqBA,CAAA;IAC3B;IACA,IAAI,IAAI,CAACoD,wBAAwB,EAAE;MACjC,IAAI,CAACA,wBAAwB,CAACC,gBAAgB,EAAE;;EAEpD;EAEAvC,eAAeA,CAAA;IACb,IAAI,IAAI,CAACJ,QAAQ,IAAI,IAAI,CAACC,MAAM,EAAE;MAChC,MAAM2C,UAAU,GAAGC,MAAM,CACvB,GAAG,IAAI,CAAC7C,QAAQ,CAAC8C,IAAI,IAAI,IAAI,CAAC9C,QAAQ,CAAC+C,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAACjD,QAAQ,CAACkD,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,MAAME,QAAQ,GAAGN,MAAM,CACrB,GAAG,IAAI,CAAC5C,MAAM,CAAC6C,IAAI,IAAI,IAAI,CAAC7C,MAAM,CAAC8C,KAAK,CACrCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAChD,MAAM,CAACiD,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACrE;MAED,IAAI,CAACR,SAAS,CAACW,UAAU,GAAGR,UAAU,CAACS,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAACZ,SAAS,CAACa,QAAQ,GAAGH,QAAQ,CAACE,MAAM,CAAC,YAAY,CAAC;MACvD,IAAI,CAACE,cAAc,GAAG,GAAGX,UAAU,CAACS,MAAM,CACxC,cAAc,CACf,MAAMF,QAAQ,CAACE,MAAM,CAAC,cAAc,CAAC,EAAE;MAExC,IAAI,CAACb,iBAAiB,EAAE;KACzB,MAAM,IAAI,IAAI,CAACxC,QAAQ,EAAE;MACxB,MAAM4C,UAAU,GAAGC,MAAM,CACvB,GAAG,IAAI,CAAC7C,QAAQ,CAAC8C,IAAI,IAAI,IAAI,CAAC9C,QAAQ,CAAC+C,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAACjD,QAAQ,CAACkD,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,IAAI,CAACR,SAAS,CAACW,UAAU,GAAGR,UAAU,CAACS,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAACZ,SAAS,CAACa,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAGX,UAAU,CAACS,MAAM,CAAC,cAAc,CAAC;KACxD,MAAM;MACL,IAAI,CAACZ,SAAS,CAACW,UAAU,GAAG,IAAI;MAChC,IAAI,CAACX,SAAS,CAACa,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACf,iBAAiB,EAAE;;EAE5B;EAEAvE,eAAeA,CAAA;IACb,IAAI,IAAI,CAACF,QAAQ,EAAE;MACjB,IAAI,CAACjB,KAAK,CACP0G,GAAG,CACF,GAAGC,WAAW,CAACC,MAAM,YAAY,IAAI,CAAC3F,QAAQ,+BAA+B,CAC9E,CACAH,SAAS,CACPC,IAAI,IAAI;QACP,IAAI,CAAC8F,WAAW,GAAG9F,IAAI,CAACA,IAAI;MAC9B,CAAC,EACAK,KAAK,IAAI;QACRC,IAAI,CAACC,IAAI,CAAC;UACRC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAEJ,KAAK,CAACK,OAAO;UACnBC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAACrB,iBAAiB,CAACsB,OAAO,CAAC,IAAI;SACvD,CAAC;MACJ,CAAC,CACF;;EAEP;EAEAkF,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAAC/G,MAAM,CAAC2B,OAAO,CAAC,gBAAgB,CAAC;MAClDqF,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,IAAI,CAACpH,MAAM,CAAC2B,OAAO,CAAC,MAAM,CAAC;UACjC0F,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACpH,MAAM,CAAC2B,OAAO,CAAC,aAAa,CAAC;UACxC0F,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACpH,MAAM,CAAC2B,OAAO,CAAC,gBAAgB,CAAC;UAC3C0F,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAAC3G,iBAAiB,EAAE;IACxB,IAAI,CAACmB,SAAS,EAAE;EAClB;EAEAyF,WAAWA,CAAA;IACT,IAAI,IAAI,CAACzD,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;EAE1C;CACD;AA/LC0D,UAAA,EADCC,YAAY,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC,6DAS1C;AApIU7H,sBAAsB,GAAA4H,UAAA,EALlC7H,SAAS,CAAC;EACT+H,QAAQ,EAAE,oBAAoB;EAC9BC,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,CAAC,iCAAiC;CAC9C,CAAC,GACWhI,sBAAsB,CA2TlC;SA3TYA,sBAAsB", "names": ["Component", "LeagueReportsComponent", "constructor", "route", "_router", "_http", "_trans", "renderer", "_loadingService", "_registrationService", "_clubService", "_translateService", "_titleService", "calendar", "formatter", "setTitle", "_getCurrentSeason", "show", "getAllSeasonActive", "subscribe", "data", "seasons", "seasonId", "id", "_getTournaments", "error", "<PERSON><PERSON>", "fire", "title", "text", "message", "icon", "confirmButtonText", "instant", "dismiss", "_getClubs", "getAllClubs", "res", "clubs", "onSelectSeason", "$event", "Promise", "resolve", "reject", "tournamentId", "refreshChildDataTable", "onSelectClub", "console", "log", "clubId", "onSelectTournament", "onSelectMatchStatus", "matchStatus", "onDateSelection", "date", "fromDate", "toDate", "isSelecting", "after", "updateDateRange", "setTimeout", "dateRangePicker", "close", "openDatePicker", "open", "onDocumentClick", "event", "clickOutsideTimeout", "clearTimeout", "handleClickOutside", "isOpen", "target", "closest", "classList", "contains", "tagName", "getAttribute", "datepickerElement", "document", "querySelector", "clearDateRange", "validateInput", "currentValue", "input", "parsed", "parse", "<PERSON><PERSON><PERSON><PERSON>", "NgbDate", "from", "isHovered", "hoveredDate", "before", "isInside", "isRange", "equals", "onDateRangeChange", "date<PERSON><PERSON><PERSON>", "leagueTableViewComponent", "refreshDataTable", "fromMoment", "moment", "year", "month", "toString", "padStart", "day", "toMoment", "start_date", "format", "end_date", "dateRangeValue", "get", "environment", "apiUrl", "tournaments", "ngOnInit", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "type", "links", "name", "isLink", "ngOnDestroy", "__decorate", "HostListener", "selector", "templateUrl", "styleUrls"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.ts"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit\r\n} from '@angular/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { Title } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'app-league-reports',\r\n  templateUrl: './league-reports.component.html',\r\n  styleUrls: ['./league-reports.component.scss'],\r\n})\r\nexport class LeagueReportsComponent implements OnInit\r\n{\r\n  public contentHeader: object;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _http: HttpClient,\r\n    public _trans: TranslateService,\r\n    public renderer: Renderer2,\r\n    public _loadingService: LoadingService,\r\n    public _registrationService: RegistrationService,\r\n    public _clubService: ClubService,\r\n    public _translateService: TranslateService,\r\n    public _titleService: Title,\r\n    private calendar: NgbCalendar,\r\n    public formatter: NgbDateParserFormatter\r\n  ) {\r\n    this._titleService.setTitle('Matches Report');\r\n  }\r\n\r\n  _getCurrentSeason() {\r\n    this._loadingService.show();\r\n    this._registrationService.getAllSeasonActive().subscribe(\r\n      (data) => {\r\n        this.seasons = data;\r\n        this.seasonId = this.seasons[0].id;\r\n        this._getTournaments(); // Fetch tournaments after getting seasons\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      },\r\n      () => {\r\n        this._loadingService.dismiss();\r\n      }\r\n    );\r\n  }\r\n\r\n  _getClubs() {\r\n    this._clubService.getAllClubs().subscribe(\r\n      (res) => {\r\n        this.clubs = res.data;\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  onSelectSeason($event: any) {\r\n    return new Promise((resolve, reject) => {\r\n      this.seasonId = $event;\r\n      this._getTournaments(); // Fetch tournaments when season changes\r\n      // Reset tournament selection when season changes\r\n      this.tournamentId = null;\r\n      this.refreshChildDataTable();\r\n      resolve(true);\r\n    });\r\n  }\r\n\r\n  onSelectClub($event: any) {\r\n    console.log(`onSelectClub: ${$event}`);\r\n    this.clubId = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onSelectTournament($event: any) {\r\n    console.log(`onSelectTournament: ${$event}`);\r\n    this.tournamentId = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onSelectMatchStatus($event: any) {\r\n    console.log(`onSelectMatchStatus: ${$event}`);\r\n    this.matchStatus = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onDateSelection(date: NgbDate) {\r\n\r\n    if (!this.fromDate && !this.toDate) {\r\n      console.log('Setting From Date');\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    } else if (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      date &&\r\n      date.after(this.fromDate)\r\n    ) {\r\n      this.toDate = date;\r\n      this.isSelecting = false;\r\n      this.updateDateRange();\r\n      setTimeout(() => {\r\n        if (this.dateRangePicker && this.dateRangePicker.close) {\r\n          this.dateRangePicker.close();\r\n        }\r\n      }, 0);\r\n    } else {\r\n      console.log('reset date form');\r\n      this.toDate = null;\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    }\r\n  }\r\n\r\n  openDatePicker() {\r\n    this.isSelecting = false; // Reset selection state when opening\r\n    if (this.dateRangePicker) {\r\n      this.dateRangePicker.open();\r\n    }\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event) {\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n\r\n    this.clickOutsideTimeout = setTimeout(() => {\r\n      this.handleClickOutside(event);\r\n    }, 50);\r\n  }\r\n\r\n  private handleClickOutside(event: Event) {\r\n    // Check if datepicker is open\r\n    if (\r\n      !this.dateRangePicker ||\r\n      !this.dateRangePicker.isOpen ||\r\n      !this.dateRangePicker.isOpen()\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (this.isSelecting) {\r\n      return;\r\n    }\r\n\r\n    const target = event.target as HTMLElement;\r\n\r\n    if (!target) return;\r\n\r\n    if (\r\n      target.closest('.btn') ||\r\n      target.classList.contains('feather') ||\r\n      target.classList.contains('icon-calendar')\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (\r\n      target.tagName === 'INPUT' &&\r\n      target.getAttribute('name') === 'daterange'\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    const datepickerElement = document.querySelector('ngb-datepicker');\r\n    if (datepickerElement && datepickerElement.contains(target)) {\r\n      return;\r\n    }\r\n\r\n    this.dateRangePicker.close();\r\n  }\r\n\r\n  clearDateRange() {\r\n    this.fromDate = null;\r\n    this.toDate = null;\r\n    this.isSelecting = false;\r\n    this.updateDateRange();\r\n  }\r\n\r\n  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {\r\n    const parsed = this.formatter.parse(input);\r\n    return parsed && this.calendar.isValid(NgbDate.from(parsed))\r\n      ? NgbDate.from(parsed)\r\n      : currentValue;\r\n  }\r\n\r\n  isHovered(date: NgbDate) {\r\n    return (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      this.hoveredDate &&\r\n      date.after(this.fromDate) &&\r\n      date.before(this.hoveredDate)\r\n    );\r\n  }\r\n\r\n  isInside(date: NgbDate) {\r\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\r\n  }\r\n\r\n  isRange(date: NgbDate) {\r\n    return (\r\n      date.equals(this.fromDate) ||\r\n      (this.toDate && date.equals(this.toDate)) ||\r\n      this.isInside(date) ||\r\n      this.isHovered(date)\r\n    );\r\n  }\r\n\r\n  onDateRangeChange() {\r\n    console.log('onDateRangeChange:', this.dateRange);\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  private refreshChildDataTable() {\r\n    // Refresh the league table view component's DataTable\r\n    if (this.leagueTableViewComponent) {\r\n      this.leagueTableViewComponent.refreshDataTable();\r\n    }\r\n  }\r\n\r\n  updateDateRange() {\r\n    if (this.fromDate && this.toDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      const toMoment = moment(\r\n        `${this.toDate.year}-${this.toDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`\r\n      );\r\n\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\r\n      this.dateRangeValue = `${fromMoment.format(\r\n        'MMM DD, YYYY'\r\n      )} - ${toMoment.format('MMM DD, YYYY')}`;\r\n\r\n      this.onDateRangeChange();\r\n    } else if (this.fromDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\r\n    } else {\r\n      this.dateRange.start_date = null;\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = '';\r\n      this.onDateRangeChange();\r\n    }\r\n  }\r\n\r\n  _getTournaments() {\r\n    if (this.seasonId) {\r\n      this._http\r\n        .get<any>(\r\n          `${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`\r\n        )\r\n        .subscribe(\r\n          (data) => {\r\n            this.tournaments = data.data;\r\n          },\r\n          (error) => {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: error.message,\r\n              icon: 'error',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n            });\r\n          }\r\n        );\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Matches Report'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Home'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Tournaments'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Matches Report'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    this._getCurrentSeason();\r\n    this._getClubs();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}