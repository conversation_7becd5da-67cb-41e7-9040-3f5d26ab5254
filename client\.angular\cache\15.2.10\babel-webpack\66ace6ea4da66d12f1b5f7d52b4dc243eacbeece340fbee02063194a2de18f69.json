{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DataTableDirective } from 'angular-datatables';\nimport { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"app/services/commons.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i5 from \"app/services/export.service\";\nimport * as i6 from \"app/services/loading.service\";\nimport * as i7 from \"app/services/registration.service\";\nimport * as i8 from \"app/services/club.service\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i11 from \"angular-datatables\";\nimport * as i12 from \"../../../../components/btn-dropdown-action/btn-dropdown-action.component\";\nimport * as i13 from \"../../../../components/editor-sidebar/editor-sidebar.component\";\nconst _c0 = [\"rowActionBtn\"];\nconst _c1 = [\"dateRangePicker\"];\nfunction LeagueTableViewComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-btn-dropdown-action\", 7);\n    i0.ɵɵlistener(\"emitter\", function LeagueTableViewComponent_ng_template_6_Template_app_btn_dropdown_action_emitter_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const emitter_r3 = restoredCtx.captureEvents;\n      return i0.ɵɵresetView(emitter_r3($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.adtData;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"actions\", ctx_r1.rowActions)(\"data\", data_r2);\n  }\n}\nexport class LeagueTableViewComponent {\n  constructor(_http, _commonsService, _translateService, _coreSidebarService, _exportService, _loadingService, _registrationService, _clubService, calendar, formatter) {\n    this._http = _http;\n    this._commonsService = _commonsService;\n    this._translateService = _translateService;\n    this._coreSidebarService = _coreSidebarService;\n    this._exportService = _exportService;\n    this._loadingService = _loadingService;\n    this._registrationService = _registrationService;\n    this._clubService = _clubService;\n    this.calendar = calendar;\n    this.formatter = formatter;\n    this.dtElement = DataTableDirective;\n    this.dtTrigger = new Subject();\n    this.dtOptions = {};\n    this.clubId = null;\n    this.tournamentId = null;\n    this.dateRange = {\n      start_date: null,\n      end_date: null\n    };\n    this.dateRangeValue = '';\n    this.hoveredDate = null;\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false;\n    this.matchStatus = 'all';\n    this.isTableLoading = false;\n    this.table_name = 'team-table';\n    this.matchStatusOptions = [{\n      label: 'All Status',\n      value: 'all'\n    }, {\n      label: 'Upcoming',\n      value: 'upcoming'\n    }, {\n      label: 'Active',\n      value: 'active'\n    }, {\n      label: 'Completed',\n      value: 'completed'\n    }, {\n      label: 'Cancelled',\n      value: 'cancelled'\n    }];\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: this._translateService.instant('Create New Tournament'),\n        edit: 'Edit team',\n        remove: 'Delete team'\n      },\n      url: `${environment.apiUrl}/teams/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this.fields = [{\n      key: 'home_score',\n      type: 'number',\n      props: {\n        label: this._translateService.instant('Home team score'),\n        placeholder: this._translateService.instant('Enter score of team'),\n        required: true\n      }\n    }, {\n      key: 'away_score',\n      type: 'number',\n      props: {\n        label: this._translateService.instant('Away team score'),\n        placeholder: this._translateService.instant('Enter score of team'),\n        required: true\n      }\n    }];\n  }\n  ngOnInit() {\n    this._getCurrentSeason();\n    this._getClubs();\n    this.buildDataTable();\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.dtTrigger.next(this.dtOptions);\n    }, 500);\n  }\n  buildDataTable() {\n    var _this = this;\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      rowId: 'id',\n      processing: true,\n      language: {\n        ...this._commonsService.dataTableDefaults.lang,\n        processing: ``\n      },\n      ajax: (dataTablesParameters, callback) => {\n        // Build query parameters for all filters\n        let params = new URLSearchParams();\n        if (this.clubId != undefined && this.clubId !== null) {\n          params.append('club_id', this.clubId);\n        }\n        if (this.tournamentId != undefined && this.tournamentId !== null) {\n          params.append('tournament_id', this.tournamentId);\n        }\n        if (this.dateRange?.start_date) {\n          params.append('start_date', this.dateRange.start_date);\n        }\n        if (this.dateRange?.end_date) {\n          params.append('end_date', this.dateRange.end_date);\n        }\n        if (this.matchStatus && this.matchStatus !== 'all') {\n          params.append('match_status', this.matchStatus);\n        }\n        const queryString = params.toString();\n        const url = `${environment.apiUrl}/seasons/${this.seasonId}/matches${queryString ? '?' + queryString : ''}`;\n        this.isTableLoading = true;\n        this._http.post(url, dataTablesParameters).subscribe(resp => {\n          this.isTableLoading = false;\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        }, error => {\n          this.isTableLoading = false;\n          console.error('Error loading table data:', error);\n          callback({\n            recordsTotal: 0,\n            recordsFiltered: 0,\n            data: []\n          });\n        });\n      },\n      responsive: false,\n      scrollX: true,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: -1\n      }],\n      columns: [{\n        title: this._translateService.instant('No.'),\n        data: null,\n        className: 'font-weight-bolder',\n        type: 'any-number',\n        render: function (data, type, row, metadata) {\n          return metadata.row + 1;\n        }\n      }, {\n        title: this._translateService.instant('Tournament'),\n        data: 'name',\n        className: 'font-weight-bolder',\n        type: 'any-number',\n        render: {\n          display: (data, type, row) => {\n            return data ?? 'TBD';\n          },\n          filter: (data, type, row) => {\n            return data;\n          }\n        }\n      }, {\n        title: this._translateService.instant('Round'),\n        data: 'round_name',\n        render: function (data) {\n          const displayData = data || 'TBD';\n          return `<div class=\"text-center\" style=\"width:max-content;\">${displayData}</div>`;\n        }\n      }, {\n        title: this._translateService.instant('Date'),\n        data: 'start_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          const displayDate = !data || data === 'TBD' ? 'TBD' : moment(data).format('YYYY-MM-DD');\n          return `<div class=\"text-center\" style=\"min-width: max-content;\">${displayDate}</div>`;\n        }\n      }, {\n        title: this._translateService.instant('Start time'),\n        data: 'start_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          return moment(data).format('HH:mm');\n        }\n      }, {\n        title: this._translateService.instant('End time'),\n        data: 'end_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          return moment(data).format('HH:mm');\n        }\n      }, {\n        title: this._translateService.instant('Location'),\n        data: 'location',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('Home Team'),\n        data: 'home_team_name',\n        className: 'text-center'\n      }, {\n        title: 'VS',\n        data: null,\n        className: 'text-center',\n        render: function (data, type, row) {\n          return 'vs';\n        }\n      }, {\n        title: this._translateService.instant('Away Team'),\n        data: 'away_team_name',\n        className: 'text-center'\n      }, {\n        title: this._translateService.instant('Home score'),\n        data: 'home_score',\n        className: 'text-center'\n      }, {\n        data: null,\n        className: 'text-center',\n        render: function (data, type, row) {\n          return '-';\n        }\n      }, {\n        title: this._translateService.instant('Away score'),\n        data: 'away_score',\n        className: 'text-center'\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: `<i class=\"fa-solid fa-file-csv mr-1\"></i> ${this._translateService.instant('Export CSV')}`,\n          extend: 'csv',\n          action: function () {\n            var _ref = _asyncToGenerator(function* (e, dt, button, config) {\n              const data = dt.buttons.exportData();\n              // Generate filename with current filter information\n              let filename = 'Matches_Report';\n              if (_this.seasonId) {\n                const season = _this.seasons?.find(s => s.id == _this.seasonId);\n                if (season) {\n                  filename += `_${season.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.tournamentId) {\n                const tournament = _this.tournaments?.find(t => t.id == _this.tournamentId);\n                if (tournament) {\n                  filename += `_${tournament.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.clubId) {\n                const club = _this.clubs?.find(c => c.id == _this.clubId);\n                if (club) {\n                  filename += `_${club.code.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.matchStatus && _this.matchStatus !== 'all') {\n                filename += `_${_this.matchStatus}`;\n              }\n              if (_this.dateRange?.start_date && _this.dateRange?.end_date) {\n                filename += `_${_this.dateRange.start_date}_to_${_this.dateRange.end_date}`;\n              } else if (_this.dateRange?.start_date) {\n                filename += `_from_${_this.dateRange.start_date}`;\n              }\n              filename += '.csv';\n              yield _this._exportService.exportCsv(data, filename);\n            });\n            return function action(_x, _x2, _x3, _x4) {\n              return _ref.apply(this, arguments);\n            };\n          }()\n        }]\n      }\n    };\n  }\n  refreshDataTable() {\n    if (this.dtElement?.dtInstance) {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n  }\n  static #_ = this.ɵfac = function LeagueTableViewComponent_Factory(t) {\n    return new (t || LeagueTableViewComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.CommonsService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.CoreSidebarService), i0.ɵɵdirectiveInject(i5.ExportService), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.RegistrationService), i0.ɵɵdirectiveInject(i8.ClubService), i0.ɵɵdirectiveInject(i9.NgbCalendar), i0.ɵɵdirectiveInject(i9.NgbDateParserFormatter));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LeagueTableViewComponent,\n    selectors: [[\"app-league-table-view\"]],\n    viewQuery: function LeagueTableViewComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rowActionBtn = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dateRangePicker = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    decls: 8,\n    vars: 6,\n    consts: [[1, \"p-1\"], [1, \"row\", \"mb-1\"], [1, \"col-12\"], [\"datatable\", \"\", 1, \"table\", \"border\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\"], [\"rowActionBtn\", \"\"], [\"btnStyle\", \"font-size:15px;color:black!important\", 3, \"actions\", \"data\", \"emitter\"]],\n    template: function LeagueTableViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"table\", 3);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(4, \"core-sidebar\", 4);\n        i0.ɵɵelement(5, \"app-editor-sidebar\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(6, LeagueTableViewComponent_ng_template_6_Template, 1, 2, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"name\", ctx.table_name);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"table\", ctx.dtElement)(\"fields\", ctx.fields)(\"params\", ctx.params);\n      }\n    },\n    dependencies: [i10.CoreSidebarComponent, i11.DataTableDirective, i12.BtnDropdownActionComponent, i13.EditorSidebarComponent],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": ";AAWA,SAASA,kBAAkB,QAAQ,oBAAoB;AAMvD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,OAAO,QAAQ,MAAM;AAK9B,OAAOC,MAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;ICRzBC,EAAA,CAAAC,cAAA,iCACkD;IADYD,EAAA,CAAAE,UAAA,qBAAAC,2FAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,UAAA,GAAAH,WAAA,CAAAI,aAAA;MAAA,OAAWT,EAAA,CAAAU,WAAA,CAAAF,UAAA,CAAAJ,MAAA,CAAe;IAAA,EAAC;IACvCJ,EAAA,CAAAW,YAAA,EAA0B;;;;;IADnDX,EAAA,CAAAY,UAAA,YAAAC,MAAA,CAAAC,UAAA,CAAsB,SAAAC,OAAA;;;ADgBjD,OAAM,MAAOC,wBAAwB;EAoEnCC,YACSC,KAAiB,EACjBC,eAA+B,EAC/BC,iBAAmC,EACnCC,mBAAuC,EACtCC,cAA6B,EAC9BC,eAA+B,EAC/BC,oBAAyC,EACzCC,YAAyB,EACxBC,QAAqB,EACtBC,SAAiC;IATjC,KAAAT,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAClB,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,YAAY,GAAZA,YAAY;IACX,KAAAC,QAAQ,GAARA,QAAQ;IACT,KAAAC,SAAS,GAATA,SAAS;IA1ElB,KAAAC,SAAS,GAAQhC,kBAAkB;IACnC,KAAAiC,SAAS,GAAyB,IAAI/B,OAAO,EAAe;IAC5D,KAAAgC,SAAS,GAAQ,EAAE;IAIZ,KAAAC,MAAM,GAAQ,IAAI;IAClB,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,SAAS,GAAQ;MAAEC,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAI,CAAE;IACrD,KAAAC,cAAc,GAAW,EAAE;IAClC,KAAAC,WAAW,GAAmB,IAAI;IAClC,KAAAC,QAAQ,GAAmB,IAAI;IAC/B,KAAAC,MAAM,GAAmB,IAAI;IAC7B,KAAAC,WAAW,GAAY,KAAK;IAErB,KAAAC,WAAW,GAAW,KAAK;IAK3B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,UAAU,GAAG,YAAY;IAEhC,KAAAC,kBAAkB,GAAG,CACnB;MAAEC,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAK,CAAE,EACrC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,EACxC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,CAC3C;IAEM,KAAAC,MAAM,GAAwB;MACnCC,SAAS,EAAE,IAAI,CAACL,UAAU;MAC1BM,KAAK,EAAE;QACLC,MAAM,EAAE,IAAI,CAAC9B,iBAAiB,CAAC+B,OAAO,CAAC,uBAAuB,CAAC;QAC/DC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE;OACT;MACDC,GAAG,EAAE,GAAGzD,WAAW,CAAC0D,MAAM,eAAe;MACzCC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACT;IAEM,KAAAC,MAAM,GAAU,CACrB;MACEC,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLhB,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAAC+B,OAAO,CAAC,iBAAiB,CAAC;QACxDW,WAAW,EAAE,IAAI,CAAC1C,iBAAiB,CAAC+B,OAAO,CAAC,qBAAqB,CAAC;QAClEY,QAAQ,EAAE;;KAEb,EACD;MACEJ,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLhB,KAAK,EAAE,IAAI,CAACzB,iBAAiB,CAAC+B,OAAO,CAAC,iBAAiB,CAAC;QACxDW,WAAW,EAAE,IAAI,CAAC1C,iBAAiB,CAAC+B,OAAO,CAAC,qBAAqB,CAAC;QAClEY,QAAQ,EAAE;;KAEb,CACF;EAaE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MACd,IAAI,CAACxC,SAAS,CAACyC,IAAI,CAAC,IAAI,CAACxC,SAAS,CAAC;IACrC,CAAC,EAAE,GAAG,CAAC;EACT;EAEAqC,cAAcA,CAAA;IAAA,IAAAI,KAAA;IACZ,IAAI,CAACzC,SAAS,GAAG;MACf0C,GAAG,EAAE,IAAI,CAACrD,eAAe,CAACsD,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE;QACR,GAAG,IAAI,CAAC1D,eAAe,CAACsD,iBAAiB,CAACK,IAAI;QAC9CF,UAAU,EAAE;OACb;MACDG,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C;QACA,IAAIlC,MAAM,GAAG,IAAImC,eAAe,EAAE;QAElC,IAAI,IAAI,CAACnD,MAAM,IAAIoD,SAAS,IAAI,IAAI,CAACpD,MAAM,KAAK,IAAI,EAAE;UACpDgB,MAAM,CAACqC,MAAM,CAAC,SAAS,EAAE,IAAI,CAACrD,MAAM,CAAC;;QAGvC,IAAI,IAAI,CAACC,YAAY,IAAImD,SAAS,IAAI,IAAI,CAACnD,YAAY,KAAK,IAAI,EAAE;UAChEe,MAAM,CAACqC,MAAM,CAAC,eAAe,EAAE,IAAI,CAACpD,YAAY,CAAC;;QAGnD,IAAI,IAAI,CAACC,SAAS,EAAEC,UAAU,EAAE;UAC9Ba,MAAM,CAACqC,MAAM,CAAC,YAAY,EAAE,IAAI,CAACnD,SAAS,CAACC,UAAU,CAAC;;QAGxD,IAAI,IAAI,CAACD,SAAS,EAAEE,QAAQ,EAAE;UAC5BY,MAAM,CAACqC,MAAM,CAAC,UAAU,EAAE,IAAI,CAACnD,SAAS,CAACE,QAAQ,CAAC;;QAGpD,IAAI,IAAI,CAACM,WAAW,IAAI,IAAI,CAACA,WAAW,KAAK,KAAK,EAAE;UAClDM,MAAM,CAACqC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC3C,WAAW,CAAC;;QAGjD,MAAM4C,WAAW,GAAGtC,MAAM,CAACuC,QAAQ,EAAE;QACrC,MAAMhC,GAAG,GAAG,GAAGzD,WAAW,CAAC0D,MAAM,YAAY,IAAI,CAACgC,QAAQ,WACxDF,WAAW,GAAG,GAAG,GAAGA,WAAW,GAAG,EACpC,EAAE;QAEF,IAAI,CAAC3C,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACxB,KAAK,CAACsE,IAAI,CAAMlC,GAAG,EAAE0B,oBAAoB,CAAC,CAACS,SAAS,CACtDC,IAAS,IAAI;UACZ,IAAI,CAAChD,cAAc,GAAG,KAAK;UAC3BuC,QAAQ,CAAC;YACPU,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrCC,IAAI,EAAEH,IAAI,CAACG;WACZ,CAAC;QACJ,CAAC,EACAC,KAAK,IAAI;UACR,IAAI,CAACpD,cAAc,GAAG,KAAK;UAC3BqD,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjDb,QAAQ,CAAC;YACPU,YAAY,EAAE,CAAC;YACfC,eAAe,EAAE,CAAC;YAClBC,IAAI,EAAE;WACP,CAAC;QACJ,CAAC,CACF;MACH,CAAC;MACDG,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,CAAC;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,CAAC;MACpDC,OAAO,EAAE,CACP;QACEpD,KAAK,EAAE,IAAI,CAAC7B,iBAAiB,CAAC+B,OAAO,CAAC,KAAK,CAAC;QAC5C0C,IAAI,EAAE,IAAI;QACVS,SAAS,EAAE,oBAAoB;QAC/B1C,IAAI,EAAE,YAAY;QAClB2C,MAAM,EAAE,SAAAA,CAAUV,IAAI,EAAEjC,IAAI,EAAE4C,GAAG,EAAEC,QAAQ;UACzC,OAAOA,QAAQ,CAACD,GAAG,GAAG,CAAC;QACzB;OACD,EACD;QACEvD,KAAK,EAAE,IAAI,CAAC7B,iBAAiB,CAAC+B,OAAO,CAAC,YAAY,CAAC;QACnD0C,IAAI,EAAE,MAAM;QACZS,SAAS,EAAE,oBAAoB;QAC/B1C,IAAI,EAAE,YAAY;QAClB2C,MAAM,EAAE;UACNG,OAAO,EAAEA,CAACb,IAAI,EAAEjC,IAAI,EAAE4C,GAAG,KAAI;YAC3B,OAAOX,IAAI,IAAI,KAAK;UACtB,CAAC;UACDc,MAAM,EAAEA,CAACd,IAAI,EAAEjC,IAAI,EAAE4C,GAAG,KAAI;YAC1B,OAAOX,IAAI;UACb;;OAEH,EACD;QACE5C,KAAK,EAAE,IAAI,CAAC7B,iBAAiB,CAAC+B,OAAO,CAAC,OAAO,CAAC;QAC9C0C,IAAI,EAAE,YAAY;QAClBU,MAAM,EAAE,SAAAA,CAAUV,IAAI;UACpB,MAAMe,WAAW,GAAGf,IAAI,IAAI,KAAK;UACjC,OAAO,uDAAuDe,WAAW,QAAQ;QACnF;OACD,EACD;QACE3D,KAAK,EAAE,IAAI,CAAC7B,iBAAiB,CAAC+B,OAAO,CAAC,MAAM,CAAC;QAC7C0C,IAAI,EAAE,YAAY;QAClBS,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAUV,IAAI,EAAEjC,IAAI,EAAE4C,GAAG;UAC/B,MAAMK,WAAW,GACf,CAAChB,IAAI,IAAIA,IAAI,KAAK,KAAK,GACnB,KAAK,GACL9F,MAAM,CAAC8F,IAAI,CAAC,CAACiB,MAAM,CAAC,YAAY,CAAC;UACvC,OAAO,4DAA4DD,WAAW,QAAQ;QACxF;OACD,EACD;QACE5D,KAAK,EAAE,IAAI,CAAC7B,iBAAiB,CAAC+B,OAAO,CAAC,YAAY,CAAC;QACnD0C,IAAI,EAAE,YAAY;QAClBS,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAASV,IAAI,EAAEjC,IAAI,EAAE4C,GAAG;UAC9B,IAAI,CAACX,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd,OAAO9F,MAAM,CAAC8F,IAAI,CAAC,CAACiB,MAAM,CAAC,OAAO,CAAC;QACrC;OACD,EACD;QACE7D,KAAK,EAAE,IAAI,CAAC7B,iBAAiB,CAAC+B,OAAO,CAAC,UAAU,CAAC;QACjD0C,IAAI,EAAE,UAAU;QAChBS,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAASV,IAAI,EAAEjC,IAAI,EAAE4C,GAAG;UAC9B,IAAI,CAACX,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd,OAAO9F,MAAM,CAAC8F,IAAI,CAAC,CAACiB,MAAM,CAAC,OAAO,CAAC;QACrC;OACD,EACD;QACE7D,KAAK,EAAE,IAAI,CAAC7B,iBAAiB,CAAC+B,OAAO,CAAC,UAAU,CAAC;QACjD0C,IAAI,EAAE,UAAU;QAChBU,MAAM,EAAE,SAAAA,CAASV,IAAI,EAAEjC,IAAI,EAAE4C,GAAG;UAC9B,IAAI,CAACX,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd,OAAOA,IAAI;QACb;OACD,EACD;QACE5C,KAAK,EAAE,IAAI,CAAC7B,iBAAiB,CAAC+B,OAAO,CAAC,WAAW,CAAC;QAClD0C,IAAI,EAAE,gBAAgB;QACtBS,SAAS,EAAE;OACZ,EACD;QACErD,KAAK,EAAE,IAAI;QACX4C,IAAI,EAAE,IAAI;QACVS,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAASV,IAAI,EAAEjC,IAAI,EAAE4C,GAAG;UAC9B,OAAO,IAAI;QACb;OACD,EACD;QACEvD,KAAK,EAAE,IAAI,CAAC7B,iBAAiB,CAAC+B,OAAO,CAAC,WAAW,CAAC;QAClD0C,IAAI,EAAE,gBAAgB;QACtBS,SAAS,EAAE;OACZ,EACD;QACErD,KAAK,EAAE,IAAI,CAAC7B,iBAAiB,CAAC+B,OAAO,CAAC,YAAY,CAAC;QACnD0C,IAAI,EAAE,YAAY;QAClBS,SAAS,EAAE;OACZ,EACD;QACET,IAAI,EAAE,IAAI;QACVS,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAASV,IAAI,EAAEjC,IAAI,EAAE4C,GAAG;UAC9B,OAAO,GAAG;QACZ;OACD,EACD;QACEvD,KAAK,EAAE,IAAI,CAAC7B,iBAAiB,CAAC+B,OAAO,CAAC,YAAY,CAAC;QACnD0C,IAAI,EAAE,YAAY;QAClBS,SAAS,EAAE;OACZ,CACF;MACDS,OAAO,EAAE;QACPvC,GAAG,EAAE,IAAI,CAACrD,eAAe,CAACsD,iBAAiB,CAACsC,OAAO,CAACvC,GAAG;QACvDuC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,6CAA6C,IAAI,CAAC5F,iBAAiB,CAAC+B,OAAO,CAC/E,YAAY,CACb,EAAE;UACH8D,MAAM,EAAE,KAAK;UACbxD,MAAM;YAAA,IAAAyD,IAAA,GAAAC,iBAAA,CAAE,WAAOC,CAAM,EAAEC,EAAO,EAAEC,MAAW,EAAEC,MAAW,EAAI;cAC1D,MAAM1B,IAAI,GAAGwB,EAAE,CAACN,OAAO,CAACS,UAAU,EAAE;cAEpC;cACA,IAAIC,QAAQ,GAAG,gBAAgB;cAE/B,IAAIlD,KAAI,CAACgB,QAAQ,EAAE;gBACjB,MAAMmC,MAAM,GAAGnD,KAAI,CAACoD,OAAO,EAAEC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,IAAIvD,KAAI,CAACgB,QAAQ,CAAC;gBAC7D,IAAImC,MAAM,EAAE;kBACVD,QAAQ,IAAI,IAAIC,MAAM,CAACK,IAAI,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAItD,IAAIzD,KAAI,CAACvC,YAAY,EAAE;gBACrB,MAAMiG,UAAU,GAAG1D,KAAI,CAAC2D,WAAW,EAAEN,IAAI,CAACO,CAAC,IAAIA,CAAC,CAACL,EAAE,IAAIvD,KAAI,CAACvC,YAAY,CAAC;gBACzE,IAAIiG,UAAU,EAAE;kBACdR,QAAQ,IAAI,IAAIQ,UAAU,CAACF,IAAI,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAI1D,IAAIzD,KAAI,CAACxC,MAAM,EAAE;gBACf,MAAMqG,IAAI,GAAG7D,KAAI,CAAC8D,KAAK,EAAET,IAAI,CAACU,CAAC,IAAIA,CAAC,CAACR,EAAE,IAAIvD,KAAI,CAACxC,MAAM,CAAC;gBACvD,IAAIqG,IAAI,EAAE;kBACRX,QAAQ,IAAI,IAAIW,IAAI,CAACG,IAAI,CAACP,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAIpD,IAAIzD,KAAI,CAAC9B,WAAW,IAAI8B,KAAI,CAAC9B,WAAW,KAAK,KAAK,EAAE;gBAClDgF,QAAQ,IAAI,IAAIlD,KAAI,CAAC9B,WAAW,EAAE;;cAGpC,IAAI8B,KAAI,CAACtC,SAAS,EAAEC,UAAU,IAAIqC,KAAI,CAACtC,SAAS,EAAEE,QAAQ,EAAE;gBAC1DsF,QAAQ,IAAI,IAAIlD,KAAI,CAACtC,SAAS,CAACC,UAAU,OAAOqC,KAAI,CAACtC,SAAS,CAACE,QAAQ,EAAE;eAC1E,MAAM,IAAIoC,KAAI,CAACtC,SAAS,EAAEC,UAAU,EAAE;gBACrCuF,QAAQ,IAAI,SAASlD,KAAI,CAACtC,SAAS,CAACC,UAAU,EAAE;;cAGlDuF,QAAQ,IAAI,MAAM;cAElB,MAAMlD,KAAI,CAACjD,cAAc,CAACkH,SAAS,CAAC3C,IAAI,EAAE4B,QAAQ,CAAC;YACrD,CAAC;YAAA,gBAAAhE,OAAAgF,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;cAAA,OAAA1B,IAAA,CAAA2B,KAAA,OAAAC,SAAA;YAAA;UAAA;SACF;;KAGN;EACH;EAEAC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACnH,SAAS,EAAEoH,UAAU,EAAE;MAC9B,IAAI,CAACpH,SAAS,CAACoH,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;QAC5DA,UAAU,CAACjE,IAAI,CAACmE,MAAM,EAAE;MAC1B,CAAC,CAAC;;EAEN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACtH,SAAS,CAACuH,WAAW,EAAE;EAC9B;EAAC,QAAAC,CAAA;qBA7UUrI,wBAAwB,EAAAhB,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAxJ,EAAA,CAAAsJ,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1J,EAAA,CAAAsJ,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA5J,EAAA,CAAAsJ,iBAAA,CAAAO,EAAA,CAAAC,kBAAA,GAAA9J,EAAA,CAAAsJ,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAhK,EAAA,CAAAsJ,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAlK,EAAA,CAAAsJ,iBAAA,CAAAa,EAAA,CAAAC,mBAAA,GAAApK,EAAA,CAAAsJ,iBAAA,CAAAe,EAAA,CAAAC,WAAA,GAAAtK,EAAA,CAAAsJ,iBAAA,CAAAiB,EAAA,CAAAC,WAAA,GAAAxK,EAAA,CAAAsJ,iBAAA,CAAAiB,EAAA,CAAAE,sBAAA;EAAA;EAAA,QAAAC,EAAA;UAAxB1J,wBAAwB;IAAA2J,SAAA;IAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;uBAGxBlL,kBAAkB;;;;;;;;;;;;;;QClC/BI,EAAA,CAAAC,cAAA,aAAiB;QAGXD,EAAA,CAAAgL,SAAA,eACQ;QACVhL,EAAA,CAAAW,YAAA,EAAM;QAIVX,EAAA,CAAAC,cAAA,sBAAqH;QACnHD,EAAA,CAAAgL,SAAA,4BACqB;QACvBhL,EAAA,CAAAW,YAAA,EAAe;QAEfX,EAAA,CAAAiL,UAAA,IAAAC,+CAAA,gCAAAlL,EAAA,CAAAmL,sBAAA,CAGc;;;QAdSnL,EAAA,CAAAoL,SAAA,GAAuB;QAAvBpL,EAAA,CAAAY,UAAA,cAAAmK,GAAA,CAAAjJ,SAAA,CAAuB,cAAAiJ,GAAA,CAAAlJ,SAAA;QAMqB7B,EAAA,CAAAoL,SAAA,GAAmB;QAAnBpL,EAAA,CAAAY,UAAA,SAAAmK,GAAA,CAAApI,UAAA,CAAmB;QAChE3C,EAAA,CAAAoL,SAAA,GAAmB;QAAnBpL,EAAA,CAAAY,UAAA,UAAAmK,GAAA,CAAAnJ,SAAA,CAAmB,WAAAmJ,GAAA,CAAArH,MAAA,YAAAqH,GAAA,CAAAhI,MAAA", "names": ["DataTableDirective", "environment", "Subject", "moment", "i0", "ɵɵelementStart", "ɵɵlistener", "LeagueTableViewComponent_ng_template_6_Template_app_btn_dropdown_action_emitter_0_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r5", "emitter_r3", "captureEvents", "ɵɵresetView", "ɵɵelementEnd", "ɵɵproperty", "ctx_r1", "rowActions", "data_r2", "LeagueTableViewComponent", "constructor", "_http", "_commonsService", "_translateService", "_coreSidebarService", "_exportService", "_loadingService", "_registrationService", "_clubService", "calendar", "formatter", "dtElement", "dtTrigger", "dtOptions", "clubId", "tournamentId", "date<PERSON><PERSON><PERSON>", "start_date", "end_date", "dateRangeValue", "hoveredDate", "fromDate", "toDate", "isSelecting", "matchStatus", "isTableLoading", "table_name", "matchStatusOptions", "label", "value", "params", "editor_id", "title", "create", "instant", "edit", "remove", "url", "apiUrl", "method", "action", "fields", "key", "type", "props", "placeholder", "required", "ngOnInit", "_getCurrentSeason", "_getClubs", "buildDataTable", "ngAfterViewInit", "setTimeout", "next", "_this", "dom", "dataTableDefaults", "select", "rowId", "processing", "language", "lang", "ajax", "dataTablesParameters", "callback", "URLSearchParams", "undefined", "append", "queryString", "toString", "seasonId", "post", "subscribe", "resp", "recordsTotal", "recordsFiltered", "data", "error", "console", "responsive", "scrollX", "columnDefs", "responsivePriority", "targets", "columns", "className", "render", "row", "metadata", "display", "filter", "displayData", "displayDate", "format", "buttons", "text", "extend", "_ref", "_asyncToGenerator", "e", "dt", "button", "config", "exportData", "filename", "season", "seasons", "find", "s", "id", "name", "replace", "tournament", "tournaments", "t", "club", "clubs", "c", "code", "exportCsv", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "refreshDataTable", "dtInstance", "then", "reload", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "CommonsService", "i3", "TranslateService", "i4", "CoreSidebarService", "i5", "ExportService", "i6", "LoadingService", "i7", "RegistrationService", "i8", "ClubService", "i9", "NgbCalendar", "NgbDateParserFormatter", "_2", "selectors", "viewQuery", "LeagueTableViewComponent_Query", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "LeagueTableViewComponent_ng_template_6_Template", "ɵɵtemplateRefExtractor", "ɵɵadvance"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-table-view\\league-table-view.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-table-view\\league-table-view.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\nimport {\n  Component,\n  OnInit,\n  ViewChild,\n  AfterViewInit,\n  OnDestroy,\n  TemplateRef,\n  HostListener\n} from '@angular/core';\nimport { TranslateService } from '@ngx-translate/core';\nimport { DataTableDirective } from 'angular-datatables';\nimport { CommonsService } from 'app/services/commons.service';\nimport { ExportService } from 'app/services/export.service';\nimport { LoadingService } from 'app/services/loading.service';\nimport { RegistrationService } from 'app/services/registration.service';\nimport { ClubService } from 'app/services/club.service';\nimport { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\nimport { EditorSidebarParams } from 'app/interfaces/editor-sidebar';\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\nimport { NgbCalendar, NgbDate, NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';\nimport moment from 'moment';\nimport Swal from 'sweetalert2';\n\n@Component({\n  selector: 'app-league-table-view',\n  templateUrl: './league-table-view.component.html',\n  styleUrls: ['./league-table-view.component.scss']\n})\nexport class LeagueTableViewComponent implements OnInit, AfterViewInit, OnDestroy {\n  @ViewChild('rowActionBtn') rowActionBtn: TemplateRef<any>;\n  @ViewChild('dateRangePicker', { static: false }) dateRangePicker: any;\n  @ViewChild(DataTableDirective, { static: false })\n  dtElement: any = DataTableDirective;\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\n  dtOptions: any = {};\n\n  // Filter properties\n  public seasonId: any;\n  public clubId: any = null;\n  public tournamentId: any = null;\n  public dateRange: any = { start_date: null, end_date: null };\n  public dateRangeValue: string = '';\n  hoveredDate: NgbDate | null = null;\n  fromDate: NgbDate | null = null;\n  toDate: NgbDate | null = null;\n  isSelecting: boolean = false;\n  private clickOutsideTimeout: any;\n  public matchStatus: string = 'all';\n  public seasons: any[];\n  public clubs: any[];\n  public tournaments: any[];\n\n  public isTableLoading: boolean = false;\n  public table_name = 'team-table';\n\n  matchStatusOptions = [\n    { label: 'All Status', value: 'all' },\n    { label: 'Upcoming', value: 'upcoming' },\n    { label: 'Active', value: 'active' },\n    { label: 'Completed', value: 'completed' },\n    { label: 'Cancelled', value: 'cancelled' },\n  ];\n\n  public params: EditorSidebarParams = {\n    editor_id: this.table_name,\n    title: {\n      create: this._translateService.instant('Create New Tournament'),\n      edit: 'Edit team',\n      remove: 'Delete team'\n    },\n    url: `${environment.apiUrl}/teams/editor`,\n    method: 'POST',\n    action: 'create'\n  };\n\n  public fields: any[] = [\n    {\n      key: 'home_score',\n      type: 'number',\n      props: {\n        label: this._translateService.instant('Home team score'),\n        placeholder: this._translateService.instant('Enter score of team'),\n        required: true,\n      }\n    },\n    {\n      key: 'away_score',\n      type: 'number',\n      props: {\n        label: this._translateService.instant('Away team score'),\n        placeholder: this._translateService.instant('Enter score of team'),\n        required: true,\n      }\n    }\n  ];\n\n  constructor(\n    public _http: HttpClient,\n    public _commonsService: CommonsService,\n    public _translateService: TranslateService,\n    public _coreSidebarService: CoreSidebarService,\n    private _exportService: ExportService,\n    public _loadingService: LoadingService,\n    public _registrationService: RegistrationService,\n    public _clubService: ClubService,\n    private calendar: NgbCalendar,\n    public formatter: NgbDateParserFormatter\n  ) {}\n\n  ngOnInit(): void {\n    this._getCurrentSeason();\n    this._getClubs();\n    this.buildDataTable();\n  }\n\n  ngAfterViewInit(): void {\n    setTimeout(() => {\n      this.dtTrigger.next(this.dtOptions);\n    }, 500);\n  }\n\n  buildDataTable(): void {\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      rowId: 'id',\n      processing: true,\n      language: {\n        ...this._commonsService.dataTableDefaults.lang,\n        processing: ``,\n      },\n      ajax: (dataTablesParameters: any, callback) => {\n        // Build query parameters for all filters\n        let params = new URLSearchParams();\n\n        if (this.clubId != undefined && this.clubId !== null) {\n          params.append('club_id', this.clubId);\n        }\n\n        if (this.tournamentId != undefined && this.tournamentId !== null) {\n          params.append('tournament_id', this.tournamentId);\n        }\n\n        if (this.dateRange?.start_date) {\n          params.append('start_date', this.dateRange.start_date);\n        }\n\n        if (this.dateRange?.end_date) {\n          params.append('end_date', this.dateRange.end_date);\n        }\n\n        if (this.matchStatus && this.matchStatus !== 'all') {\n          params.append('match_status', this.matchStatus);\n        }\n\n        const queryString = params.toString();\n        const url = `${environment.apiUrl}/seasons/${this.seasonId}/matches${\n          queryString ? '?' + queryString : ''\n        }`;\n\n        this.isTableLoading = true;\n        this._http.post<any>(url, dataTablesParameters).subscribe(\n          (resp: any) => {\n            this.isTableLoading = false;\n            callback({\n              recordsTotal: resp.recordsTotal,\n              recordsFiltered: resp.recordsFiltered,\n              data: resp.data,\n            });\n          },\n          (error) => {\n            this.isTableLoading = false;\n            console.error('Error loading table data:', error);\n            callback({\n              recordsTotal: 0,\n              recordsFiltered: 0,\n              data: [],\n            });\n          }\n        );\n      },\n      responsive: false,\n      scrollX: true,\n      columnDefs: [{ responsivePriority: 1, targets: -1 }],\n      columns: [\n        {\n          title: this._translateService.instant('No.'),\n          data: null,\n          className: 'font-weight-bolder',\n          type: 'any-number',\n          render: function (data, type, row, metadata) {\n            return metadata.row + 1\n          }\n        },\n        {\n          title: this._translateService.instant('Tournament'),\n          data: 'name',\n          className: 'font-weight-bolder',\n          type: 'any-number',\n          render: {\n            display: (data, type, row) => {\n              return data ?? 'TBD';\n            },\n            filter: (data, type, row) => {\n              return data;\n            }\n          }\n        },\n        {\n          title: this._translateService.instant('Round'),\n          data: 'round_name',\n          render: function (data) {\n            const displayData = data || 'TBD';\n            return `<div class=\"text-center\" style=\"width:max-content;\">${displayData}</div>`;\n          },\n        },\n        {\n          title: this._translateService.instant('Date'),\n          data: 'start_time',\n          className: 'text-center',\n          render: function (data, type, row) {\n            const displayDate =\n              !data || data === 'TBD'\n                ? 'TBD'\n                : moment(data).format('YYYY-MM-DD');\n            return `<div class=\"text-center\" style=\"min-width: max-content;\">${displayDate}</div>`;\n          },\n        },\n        {\n          title: this._translateService.instant('Start time'),\n          data: 'start_time',\n          className: 'text-center',\n          render: function(data, type, row) {\n            if (!data || data == 'TBD') {\n              return 'TBD';\n            }\n            return moment(data).format('HH:mm');\n          }\n        },\n        {\n          title: this._translateService.instant('End time'),\n          data: 'end_time',\n          className: 'text-center',\n          render: function(data, type, row) {\n            if (!data || data == 'TBD') {\n              return 'TBD';\n            }\n            return moment(data).format('HH:mm');\n          }\n        },\n        {\n          title: this._translateService.instant('Location'),\n          data: 'location',\n          render: function(data, type, row) {\n            if (!data || data == 'TBD') {\n              return 'TBD';\n            }\n            return data;\n          }\n        },\n        {\n          title: this._translateService.instant('Home Team'),\n          data: 'home_team_name',\n          className: 'text-center'\n        },\n        {\n          title: 'VS',\n          data: null,\n          className: 'text-center',\n          render: function(data, type, row) {\n            return 'vs';\n          }\n        },\n        {\n          title: this._translateService.instant('Away Team'),\n          data: 'away_team_name',\n          className: 'text-center'\n        },\n        {\n          title: this._translateService.instant('Home score'),\n          data: 'home_score',\n          className: 'text-center'\n        },\n        {\n          data: null,\n          className: 'text-center',\n          render: function(data, type, row) {\n            return '-';\n          }\n        },\n        {\n          title: this._translateService.instant('Away score'),\n          data: 'away_score',\n          className: 'text-center'\n        }\n      ],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [\n          {\n            text: `<i class=\"fa-solid fa-file-csv mr-1\"></i> ${this._translateService.instant(\n              'Export CSV'\n            )}`,\n            extend: 'csv',\n            action: async (e: any, dt: any, button: any, config: any) => {\n              const data = dt.buttons.exportData();\n              \n              // Generate filename with current filter information\n              let filename = 'Matches_Report';\n              \n              if (this.seasonId) {\n                const season = this.seasons?.find(s => s.id == this.seasonId);\n                if (season) {\n                  filename += `_${season.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              \n              if (this.tournamentId) {\n                const tournament = this.tournaments?.find(t => t.id == this.tournamentId);\n                if (tournament) {\n                  filename += `_${tournament.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              \n              if (this.clubId) {\n                const club = this.clubs?.find(c => c.id == this.clubId);\n                if (club) {\n                  filename += `_${club.code.replace(/\\s+/g, '_')}`;\n                }\n              }\n              \n              if (this.matchStatus && this.matchStatus !== 'all') {\n                filename += `_${this.matchStatus}`;\n              }\n              \n              if (this.dateRange?.start_date && this.dateRange?.end_date) {\n                filename += `_${this.dateRange.start_date}_to_${this.dateRange.end_date}`;\n              } else if (this.dateRange?.start_date) {\n                filename += `_from_${this.dateRange.start_date}`;\n              }\n              \n              filename += '.csv';\n              \n              await this._exportService.exportCsv(data, filename);\n            }\n          }\n        ]\n      }\n    };\n  }\n\n  refreshDataTable() {\n    if (this.dtElement?.dtInstance) {\n      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\n        dtInstance.ajax.reload();\n      });\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.dtTrigger.unsubscribe();\n  }\n}\n", "<div class=\"p-1\">\n  <div class=\"row mb-1\">\n    <div class=\"col-12\">\n      <table datatable [dtOptions]=\"dtOptions\" [dtTrigger]=\"dtTrigger\" class=\"table border row-border hover\">\n      </table>\n    </div>\n  </div>\n</div>\n\n<core-sidebar class=\"modal modal-slide-in sidebar-todo-modal fade\" [name]=\"table_name\" overlayClass=\"modal-backdrop\">\n  <app-editor-sidebar [table]=\"dtElement\" [fields]=\"fields\" [params]=\"params\">\n  </app-editor-sidebar>\n</core-sidebar>\n\n<ng-template #rowActionBtn let-data=\"adtData\" let-emitter=\"captureEvents\">\n  <app-btn-dropdown-action [actions]=\"rowActions\" [data]=\"data\" (emitter)=\"emitter($event)\"\n    btnStyle=\"font-size:15px;color:black!important\"></app-btn-dropdown-action>\n</ng-template>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}