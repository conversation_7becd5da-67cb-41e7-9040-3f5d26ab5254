{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nlet LeagueReportsComponent = class LeagueReportsComponent {\n  constructor(route, _router, _http, _trans, renderer, _loadingService, _registrationService, _clubService, _translateService, _titleService, calendar, formatter) {\n    this.route = route;\n    this._router = _router;\n    this._http = _http;\n    this._trans = _trans;\n    this.renderer = renderer;\n    this._loadingService = _loadingService;\n    this._registrationService = _registrationService;\n    this._clubService = _clubService;\n    this._translateService = _translateService;\n    this._titleService = _titleService;\n    this.calendar = calendar;\n    this.formatter = formatter;\n    this.clubId = null;\n    this.tournamentId = null;\n    this.dateRange = {\n      start_date: null,\n      end_date: null\n    };\n    this.dateRangeValue = '';\n    this.hoveredDate = null;\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false; // Track if we're in the middle of selecting range\n    this.matchStatus = 'all';\n    this.matchStatusOptions = [{\n      label: 'All Status',\n      value: 'all'\n    }, {\n      label: 'Upcoming',\n      value: 'upcoming'\n    }, {\n      label: 'Active',\n      value: 'active'\n    }, {\n      label: 'Completed',\n      value: 'completed'\n    }, {\n      label: 'Cancelled',\n      value: 'cancelled'\n    }];\n    this._titleService.setTitle('Matches Report');\n  }\n  _getCurrentSeason() {\n    this._loadingService.show();\n    this._registrationService.getAllSeasonActive().subscribe(data => {\n      this.seasons = data;\n      this.seasonId = this.seasons[0].id;\n      this._getTournaments(); // Fetch tournaments after getting seasons\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    }, () => {\n      this._loadingService.dismiss();\n    });\n  }\n  _getClubs() {\n    this._clubService.getAllClubs().subscribe(res => {\n      this.clubs = res.data;\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  onSelectSeason($event) {\n    return new Promise((resolve, reject) => {\n      this.seasonId = $event;\n      this._getTournaments(); // Fetch tournaments when season changes\n      // Reset tournament selection when season changes\n      this.tournamentId = null;\n      this.refreshChildDataTable();\n      resolve(true);\n    });\n  }\n  onSelectClub($event) {\n    console.log(`onSelectClub: ${$event}`);\n    this.clubId = $event;\n    this.refreshChildDataTable();\n  }\n  onSelectTournament($event) {\n    console.log(`onSelectTournament: ${$event}`);\n    this.tournamentId = $event;\n    this.refreshChildDataTable();\n  }\n  onSelectMatchStatus($event) {\n    console.log(`onSelectMatchStatus: ${$event}`);\n    this.matchStatus = $event;\n    this.refreshChildDataTable();\n  }\n  onDateSelection(date) {\n    if (!this.fromDate && !this.toDate) {\n      console.log('Setting From Date');\n      this.fromDate = date;\n      this.isSelecting = true;\n    } else if (this.fromDate && !this.toDate && date && date.after(this.fromDate)) {\n      this.toDate = date;\n      this.isSelecting = false;\n      this.updateDateRange();\n      setTimeout(() => {\n        if (this.dateRangePicker && this.dateRangePicker.close) {\n          this.dateRangePicker.close();\n        }\n      }, 0);\n    } else {\n      console.log('reset date form');\n      this.toDate = null;\n      this.fromDate = date;\n      this.isSelecting = true;\n    }\n  }\n  openDatePicker() {\n    this.isSelecting = false; // Reset selection state when opening\n    if (this.dateRangePicker) {\n      this.dateRangePicker.open();\n    }\n  }\n  onDocumentClick(event) {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n    this.clickOutsideTimeout = setTimeout(() => {\n      this.handleClickOutside(event);\n    }, 50);\n  }\n  handleClickOutside(event) {\n    // Check if datepicker is open\n    if (!this.dateRangePicker || !this.dateRangePicker.isOpen || !this.dateRangePicker.isOpen()) {\n      return;\n    }\n    if (this.isSelecting) {\n      return;\n    }\n    const target = event.target;\n    if (!target) return;\n    if (target.closest('.btn') || target.classList.contains('feather') || target.classList.contains('icon-calendar')) {\n      return;\n    }\n    if (target.tagName === 'INPUT' && target.getAttribute('name') === 'daterange') {\n      return;\n    }\n    const datepickerElement = document.querySelector('ngb-datepicker');\n    if (datepickerElement && datepickerElement.contains(target)) {\n      return;\n    }\n    this.dateRangePicker.close();\n  }\n  clearDateRange() {\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false;\n    this.updateDateRange();\n  }\n  validateInput(currentValue, input) {\n    const parsed = this.formatter.parse(input);\n    return parsed && this.calendar.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;\n  }\n  isHovered(date) {\n    return this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate);\n  }\n  isInside(date) {\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\n  }\n  isRange(date) {\n    return date.equals(this.fromDate) || this.toDate && date.equals(this.toDate) || this.isInside(date) || this.isHovered(date);\n  }\n  onDateRangeChange() {\n    console.log('onDateRangeChange:', this.dateRange);\n    this.refreshChildDataTable();\n  }\n  refreshChildDataTable() {\n    // Refresh the league table view component's DataTable\n    if (this.leagueTableViewComponent) {\n      this.leagueTableViewComponent.refreshDataTable();\n    }\n  }\n  updateDateRange() {\n    if (this.fromDate && this.toDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      const toMoment = moment(`${this.toDate.year}-${this.toDate.month.toString().padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\n      this.dateRangeValue = `${fromMoment.format('MMM DD, YYYY')} - ${toMoment.format('MMM DD, YYYY')}`;\n      this.onDateRangeChange();\n    } else if (this.fromDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = null;\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\n    } else {\n      this.dateRange.start_date = null;\n      this.dateRange.end_date = null;\n      this.dateRangeValue = '';\n      this.onDateRangeChange();\n    }\n  }\n  _getTournaments() {\n    if (this.seasonId) {\n      this._http.get(`${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`).subscribe(data => {\n        this.tournaments = data.data;\n      }, error => {\n        Swal.fire({\n          title: 'Error',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      });\n    }\n  }\n  ngOnInit() {\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Matches Report'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Home'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Tournaments'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Matches Report'),\n          isLink: false\n        }]\n      }\n    };\n    this._getCurrentSeason();\n    this._getClubs();\n  }\n  ngOnDestroy() {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n  }\n};\n__decorate([ViewChild('dateRangePicker', {\n  static: false\n})], LeagueReportsComponent.prototype, \"dateRangePicker\", void 0);\n__decorate([ViewChild(LeagueTableViewComponent, {\n  static: false\n})], LeagueReportsComponent.prototype, \"leagueTableViewComponent\", void 0);\n__decorate([HostListener('document:click', ['$event'])], LeagueReportsComponent.prototype, \"onDocumentClick\", null);\nLeagueReportsComponent = __decorate([Component({\n  selector: 'app-league-reports',\n  templateUrl: './league-reports.component.html',\n  styleUrls: ['./league-reports.component.scss']\n})], LeagueReportsComponent);\nexport { LeagueReportsComponent };", "map": {"version": 3, "mappings": ";AAAA,SACEA,SAAS,QAEJ,eAAe;AASf,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EA6BjCC,YACUC,KAAqB,EACtBC,OAAe,EACfC,KAAiB,EACjBC,MAAwB,EACxBC,QAAmB,EACnBC,eAA+B,EAC/BC,oBAAyC,EACzCC,YAAyB,EACzBC,iBAAmC,EACnCC,aAAoB,EACnBC,QAAqB,EACtBC,SAAiC;IAXhC,KAAAX,KAAK,GAALA,KAAK;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,QAAQ,GAARA,QAAQ;IACT,KAAAC,SAAS,GAATA,SAAS;IAnCX,KAAAC,MAAM,GAAQ,IAAI;IAClB,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,SAAS,GAAQ;MAAEC,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAI,CAAE;IACrD,KAAAC,cAAc,GAAW,EAAE;IAClC,KAAAC,WAAW,GAAmB,IAAI;IAClC,KAAAC,QAAQ,GAAmB,IAAI;IAC/B,KAAAC,MAAM,GAAmB,IAAI;IAC7B,KAAAC,WAAW,GAAY,KAAK,CAAC,CAAC;IAEvB,KAAAC,WAAW,GAAW,KAAK;IAMlC,KAAAC,kBAAkB,GAAG,CACnB;MAAEC,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAK,CAAE,EACrC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,EACxC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,CAC3C;IAgBC,IAAI,CAAChB,aAAa,CAACiB,QAAQ,CAAC,gBAAgB,CAAC;EAC/C;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACtB,eAAe,CAACuB,IAAI,EAAE;IAC3B,IAAI,CAACtB,oBAAoB,CAACuB,kBAAkB,EAAE,CAACC,SAAS,CACrDC,IAAI,IAAI;MACP,IAAI,CAACC,OAAO,GAAGD,IAAI;MACnB,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACD,OAAO,CAAC,CAAC,CAAC,CAACE,EAAE;MAClC,IAAI,CAACC,eAAe,EAAE,CAAC,CAAC;IAC1B,CAAC,EACAC,KAAK,IAAI;MACRC,IAAI,CAACC,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEJ,KAAK,CAACK,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACnC,iBAAiB,CAACoC,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,EACD,MAAK;MACH,IAAI,CAACvC,eAAe,CAACwC,OAAO,EAAE;IAChC,CAAC,CACF;EACH;EAEAC,SAASA,CAAA;IACP,IAAI,CAACvC,YAAY,CAACwC,WAAW,EAAE,CAACjB,SAAS,CACtCkB,GAAG,IAAI;MACN,IAAI,CAACC,KAAK,GAAGD,GAAG,CAACjB,IAAI;IACvB,CAAC,EACAK,KAAK,IAAI;MACRC,IAAI,CAACC,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEJ,KAAK,CAACK,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACnC,iBAAiB,CAACoC,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEAM,cAAcA,CAACC,MAAW;IACxB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAACrB,QAAQ,GAAGkB,MAAM;MACtB,IAAI,CAAChB,eAAe,EAAE,CAAC,CAAC;MACxB;MACA,IAAI,CAACtB,YAAY,GAAG,IAAI;MACxB,IAAI,CAAC0C,qBAAqB,EAAE;MAC5BF,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;EACJ;EAEAG,YAAYA,CAACL,MAAW;IACtBM,OAAO,CAACC,GAAG,CAAC,iBAAiBP,MAAM,EAAE,CAAC;IACtC,IAAI,CAACvC,MAAM,GAAGuC,MAAM;IACpB,IAAI,CAACI,qBAAqB,EAAE;EAC9B;EAEAI,kBAAkBA,CAACR,MAAW;IAC5BM,OAAO,CAACC,GAAG,CAAC,uBAAuBP,MAAM,EAAE,CAAC;IAC5C,IAAI,CAACtC,YAAY,GAAGsC,MAAM;IAC1B,IAAI,CAACI,qBAAqB,EAAE;EAC9B;EAEAK,mBAAmBA,CAACT,MAAW;IAC7BM,OAAO,CAACC,GAAG,CAAC,wBAAwBP,MAAM,EAAE,CAAC;IAC7C,IAAI,CAAC7B,WAAW,GAAG6B,MAAM;IACzB,IAAI,CAACI,qBAAqB,EAAE;EAC9B;EAEAM,eAAeA,CAACC,IAAa;IAE3B,IAAI,CAAC,IAAI,CAAC3C,QAAQ,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MAClCqC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,IAAI,CAACvC,QAAQ,GAAG2C,IAAI;MACpB,IAAI,CAACzC,WAAW,GAAG,IAAI;KACxB,MAAM,IACL,IAAI,CAACF,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZ0C,IAAI,IACJA,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC5C,QAAQ,CAAC,EACzB;MACA,IAAI,CAACC,MAAM,GAAG0C,IAAI;MAClB,IAAI,CAACzC,WAAW,GAAG,KAAK;MACxB,IAAI,CAAC2C,eAAe,EAAE;MACtBC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACC,eAAe,IAAI,IAAI,CAACA,eAAe,CAACC,KAAK,EAAE;UACtD,IAAI,CAACD,eAAe,CAACC,KAAK,EAAE;;MAEhC,CAAC,EAAE,CAAC,CAAC;KACN,MAAM;MACLV,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B,IAAI,CAACtC,MAAM,GAAG,IAAI;MAClB,IAAI,CAACD,QAAQ,GAAG2C,IAAI;MACpB,IAAI,CAACzC,WAAW,GAAG,IAAI;;EAE3B;EAEA+C,cAAcA,CAAA;IACZ,IAAI,CAAC/C,WAAW,GAAG,KAAK,CAAC,CAAC;IAC1B,IAAI,IAAI,CAAC6C,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACG,IAAI,EAAE;;EAE/B;EAGAC,eAAeA,CAACC,KAAY;IAC1B,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;IAGxC,IAAI,CAACA,mBAAmB,GAAGP,UAAU,CAAC,MAAK;MACzC,IAAI,CAACS,kBAAkB,CAACH,KAAK,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC;EACR;EAEQG,kBAAkBA,CAACH,KAAY;IACrC;IACA,IACE,CAAC,IAAI,CAACL,eAAe,IACrB,CAAC,IAAI,CAACA,eAAe,CAACS,MAAM,IAC5B,CAAC,IAAI,CAACT,eAAe,CAACS,MAAM,EAAE,EAC9B;MACA;;IAGF,IAAI,IAAI,CAACtD,WAAW,EAAE;MACpB;;IAGF,MAAMuD,MAAM,GAAGL,KAAK,CAACK,MAAqB;IAE1C,IAAI,CAACA,MAAM,EAAE;IAEb,IACEA,MAAM,CAACC,OAAO,CAAC,MAAM,CAAC,IACtBD,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,SAAS,CAAC,IACpCH,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,EAC1C;MACA;;IAGF,IACEH,MAAM,CAACI,OAAO,KAAK,OAAO,IAC1BJ,MAAM,CAACK,YAAY,CAAC,MAAM,CAAC,KAAK,WAAW,EAC3C;MACA;;IAGF,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;IAClE,IAAIF,iBAAiB,IAAIA,iBAAiB,CAACH,QAAQ,CAACH,MAAM,CAAC,EAAE;MAC3D;;IAGF,IAAI,CAACV,eAAe,CAACC,KAAK,EAAE;EAC9B;EAEAkB,cAAcA,CAAA;IACZ,IAAI,CAAClE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC2C,eAAe,EAAE;EACxB;EAEAsB,aAAaA,CAACC,YAA4B,EAAEC,KAAa;IACvD,MAAMC,MAAM,GAAG,IAAI,CAAC9E,SAAS,CAAC+E,KAAK,CAACF,KAAK,CAAC;IAC1C,OAAOC,MAAM,IAAI,IAAI,CAAC/E,QAAQ,CAACiF,OAAO,CAACC,OAAO,CAACC,IAAI,CAACJ,MAAM,CAAC,CAAC,GACxDG,OAAO,CAACC,IAAI,CAACJ,MAAM,CAAC,GACpBF,YAAY;EAClB;EAEAO,SAASA,CAAChC,IAAa;IACrB,OACE,IAAI,CAAC3C,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZ,IAAI,CAACF,WAAW,IAChB4C,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC5C,QAAQ,CAAC,IACzB2C,IAAI,CAACiC,MAAM,CAAC,IAAI,CAAC7E,WAAW,CAAC;EAEjC;EAEA8E,QAAQA,CAAClC,IAAa;IACpB,OAAO,IAAI,CAAC1C,MAAM,IAAI0C,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC5C,QAAQ,CAAC,IAAI2C,IAAI,CAACiC,MAAM,CAAC,IAAI,CAAC3E,MAAM,CAAC;EAC7E;EAEA6E,OAAOA,CAACnC,IAAa;IACnB,OACEA,IAAI,CAACoC,MAAM,CAAC,IAAI,CAAC/E,QAAQ,CAAC,IACzB,IAAI,CAACC,MAAM,IAAI0C,IAAI,CAACoC,MAAM,CAAC,IAAI,CAAC9E,MAAM,CAAE,IACzC,IAAI,CAAC4E,QAAQ,CAAClC,IAAI,CAAC,IACnB,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAAC;EAExB;EAEAqC,iBAAiBA,CAAA;IACf1C,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC5C,SAAS,CAAC;IACjD,IAAI,CAACyC,qBAAqB,EAAE;EAC9B;EAEQA,qBAAqBA,CAAA;IAC3B;IACA,IAAI,IAAI,CAAC6C,wBAAwB,EAAE;MACjC,IAAI,CAACA,wBAAwB,CAACC,gBAAgB,EAAE;;EAEpD;EAEArC,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC7C,QAAQ,IAAI,IAAI,CAACC,MAAM,EAAE;MAChC,MAAMkF,UAAU,GAAGC,MAAM,CACvB,GAAG,IAAI,CAACpF,QAAQ,CAACqF,IAAI,IAAI,IAAI,CAACrF,QAAQ,CAACsF,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAACxF,QAAQ,CAACyF,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,MAAME,QAAQ,GAAGN,MAAM,CACrB,GAAG,IAAI,CAACnF,MAAM,CAACoF,IAAI,IAAI,IAAI,CAACpF,MAAM,CAACqF,KAAK,CACrCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAACvF,MAAM,CAACwF,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACrE;MAED,IAAI,CAAC7F,SAAS,CAACC,UAAU,GAAGuF,UAAU,CAACQ,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAAChG,SAAS,CAACE,QAAQ,GAAG6F,QAAQ,CAACC,MAAM,CAAC,YAAY,CAAC;MACvD,IAAI,CAAC7F,cAAc,GAAG,GAAGqF,UAAU,CAACQ,MAAM,CACxC,cAAc,CACf,MAAMD,QAAQ,CAACC,MAAM,CAAC,cAAc,CAAC,EAAE;MAExC,IAAI,CAACX,iBAAiB,EAAE;KACzB,MAAM,IAAI,IAAI,CAAChF,QAAQ,EAAE;MACxB,MAAMmF,UAAU,GAAGC,MAAM,CACvB,GAAG,IAAI,CAACpF,QAAQ,CAACqF,IAAI,IAAI,IAAI,CAACrF,QAAQ,CAACsF,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAACxF,QAAQ,CAACyF,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,IAAI,CAAC7F,SAAS,CAACC,UAAU,GAAGuF,UAAU,CAACQ,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAAChG,SAAS,CAACE,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAGqF,UAAU,CAACQ,MAAM,CAAC,cAAc,CAAC;KACxD,MAAM;MACL,IAAI,CAAChG,SAAS,CAACC,UAAU,GAAG,IAAI;MAChC,IAAI,CAACD,SAAS,CAACE,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACkF,iBAAiB,EAAE;;EAE5B;EAEAhE,eAAeA,CAAA;IACb,IAAI,IAAI,CAACF,QAAQ,EAAE;MACjB,IAAI,CAAC/B,KAAK,CACP6G,GAAG,CACF,GAAGC,WAAW,CAACC,MAAM,YAAY,IAAI,CAAChF,QAAQ,+BAA+B,CAC9E,CACAH,SAAS,CACPC,IAAI,IAAI;QACP,IAAI,CAACmF,WAAW,GAAGnF,IAAI,CAACA,IAAI;MAC9B,CAAC,EACAK,KAAK,IAAI;QACRC,IAAI,CAACC,IAAI,CAAC;UACRC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAEJ,KAAK,CAACK,OAAO;UACnBC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAACnC,iBAAiB,CAACoC,OAAO,CAAC,IAAI;SACvD,CAAC;MACJ,CAAC,CACF;;EAEP;EAEAuE,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAAClH,MAAM,CAACyC,OAAO,CAAC,gBAAgB,CAAC;MAClD0E,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,IAAI,CAACvH,MAAM,CAACyC,OAAO,CAAC,MAAM,CAAC;UACjC+E,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACvH,MAAM,CAACyC,OAAO,CAAC,aAAa,CAAC;UACxC+E,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACvH,MAAM,CAACyC,OAAO,CAAC,gBAAgB,CAAC;UAC3C+E,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAAChG,iBAAiB,EAAE;IACxB,IAAI,CAACmB,SAAS,EAAE;EAClB;EAEA8E,WAAWA,CAAA;IACT,IAAI,IAAI,CAACpD,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;EAE1C;CACD;AAlVkDqD,UAAA,EAAhDC,SAAS,CAAC,iBAAiB,EAAE;EAAEC,MAAM,EAAE;AAAK,CAAE,CAAC,+DAAsB;AACdF,UAAA,EAAvDC,SAAS,CAACE,wBAAwB,EAAE;EAAED,MAAM,EAAE;AAAK,CAAE,CAAC,wEAAoD;AAkJ3GF,UAAA,EADCI,YAAY,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC,6DAS1C;AA7JUnI,sBAAsB,GAAA+H,UAAA,EALlChI,SAAS,CAAC;EACTqI,QAAQ,EAAE,oBAAoB;EAC9BC,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,CAAC,iCAAiC;CAC9C,CAAC,GACWtI,sBAAsB,CAoVlC;SApVYA,sBAAsB", "names": ["Component", "LeagueReportsComponent", "constructor", "route", "_router", "_http", "_trans", "renderer", "_loadingService", "_registrationService", "_clubService", "_translateService", "_titleService", "calendar", "formatter", "clubId", "tournamentId", "date<PERSON><PERSON><PERSON>", "start_date", "end_date", "dateRangeValue", "hoveredDate", "fromDate", "toDate", "isSelecting", "matchStatus", "matchStatusOptions", "label", "value", "setTitle", "_getCurrentSeason", "show", "getAllSeasonActive", "subscribe", "data", "seasons", "seasonId", "id", "_getTournaments", "error", "<PERSON><PERSON>", "fire", "title", "text", "message", "icon", "confirmButtonText", "instant", "dismiss", "_getClubs", "getAllClubs", "res", "clubs", "onSelectSeason", "$event", "Promise", "resolve", "reject", "refreshChildDataTable", "onSelectClub", "console", "log", "onSelectTournament", "onSelectMatchStatus", "onDateSelection", "date", "after", "updateDateRange", "setTimeout", "dateRangePicker", "close", "openDatePicker", "open", "onDocumentClick", "event", "clickOutsideTimeout", "clearTimeout", "handleClickOutside", "isOpen", "target", "closest", "classList", "contains", "tagName", "getAttribute", "datepickerElement", "document", "querySelector", "clearDateRange", "validateInput", "currentValue", "input", "parsed", "parse", "<PERSON><PERSON><PERSON><PERSON>", "NgbDate", "from", "isHovered", "before", "isInside", "isRange", "equals", "onDateRangeChange", "leagueTableViewComponent", "refreshDataTable", "fromMoment", "moment", "year", "month", "toString", "padStart", "day", "toMoment", "format", "get", "environment", "apiUrl", "tournaments", "ngOnInit", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "type", "links", "name", "isLink", "ngOnDestroy", "__decorate", "ViewChild", "static", "LeagueTableViewComponent", "HostListener", "selector", "templateUrl", "styleUrls"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.ts"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit\r\n} from '@angular/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { Title } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'app-league-reports',\r\n  templateUrl: './league-reports.component.html',\r\n  styleUrls: ['./league-reports.component.scss'],\r\n})\r\nexport class LeagueReportsComponent implements OnInit, OnDestroy\r\n{\r\n  @ViewChild('dateRangePicker', { static: false }) dateRangePicker: any;\r\n  @ViewChild(LeagueTableViewComponent, { static: false }) leagueTableViewComponent: LeagueTableViewComponent;\r\n\r\n  public seasonId: any;\r\n  public clubId: any = null;\r\n  public tournamentId: any = null;\r\n  public dateRange: any = { start_date: null, end_date: null };\r\n  public dateRangeValue: string = '';\r\n  hoveredDate: NgbDate | null = null;\r\n  fromDate: NgbDate | null = null;\r\n  toDate: NgbDate | null = null;\r\n  isSelecting: boolean = false; // Track if we're in the middle of selecting range\r\n  private clickOutsideTimeout: any;\r\n  public matchStatus: string = 'all';\r\n  public contentHeader: object;\r\n  public seasons: any[];\r\n  public clubs: any[];\r\n  public tournaments: any[];\r\n\r\n  matchStatusOptions = [\r\n    { label: 'All Status', value: 'all' },\r\n    { label: 'Upcoming', value: 'upcoming' },\r\n    { label: 'Active', value: 'active' },\r\n    { label: 'Completed', value: 'completed' },\r\n    { label: 'Cancelled', value: 'cancelled' },\r\n  ];\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _http: HttpClient,\r\n    public _trans: TranslateService,\r\n    public renderer: Renderer2,\r\n    public _loadingService: LoadingService,\r\n    public _registrationService: RegistrationService,\r\n    public _clubService: ClubService,\r\n    public _translateService: TranslateService,\r\n    public _titleService: Title,\r\n    private calendar: NgbCalendar,\r\n    public formatter: NgbDateParserFormatter\r\n  ) {\r\n    this._titleService.setTitle('Matches Report');\r\n  }\r\n\r\n  _getCurrentSeason() {\r\n    this._loadingService.show();\r\n    this._registrationService.getAllSeasonActive().subscribe(\r\n      (data) => {\r\n        this.seasons = data;\r\n        this.seasonId = this.seasons[0].id;\r\n        this._getTournaments(); // Fetch tournaments after getting seasons\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      },\r\n      () => {\r\n        this._loadingService.dismiss();\r\n      }\r\n    );\r\n  }\r\n\r\n  _getClubs() {\r\n    this._clubService.getAllClubs().subscribe(\r\n      (res) => {\r\n        this.clubs = res.data;\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  onSelectSeason($event: any) {\r\n    return new Promise((resolve, reject) => {\r\n      this.seasonId = $event;\r\n      this._getTournaments(); // Fetch tournaments when season changes\r\n      // Reset tournament selection when season changes\r\n      this.tournamentId = null;\r\n      this.refreshChildDataTable();\r\n      resolve(true);\r\n    });\r\n  }\r\n\r\n  onSelectClub($event: any) {\r\n    console.log(`onSelectClub: ${$event}`);\r\n    this.clubId = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onSelectTournament($event: any) {\r\n    console.log(`onSelectTournament: ${$event}`);\r\n    this.tournamentId = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onSelectMatchStatus($event: any) {\r\n    console.log(`onSelectMatchStatus: ${$event}`);\r\n    this.matchStatus = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onDateSelection(date: NgbDate) {\r\n\r\n    if (!this.fromDate && !this.toDate) {\r\n      console.log('Setting From Date');\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    } else if (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      date &&\r\n      date.after(this.fromDate)\r\n    ) {\r\n      this.toDate = date;\r\n      this.isSelecting = false;\r\n      this.updateDateRange();\r\n      setTimeout(() => {\r\n        if (this.dateRangePicker && this.dateRangePicker.close) {\r\n          this.dateRangePicker.close();\r\n        }\r\n      }, 0);\r\n    } else {\r\n      console.log('reset date form');\r\n      this.toDate = null;\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    }\r\n  }\r\n\r\n  openDatePicker() {\r\n    this.isSelecting = false; // Reset selection state when opening\r\n    if (this.dateRangePicker) {\r\n      this.dateRangePicker.open();\r\n    }\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event) {\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n\r\n    this.clickOutsideTimeout = setTimeout(() => {\r\n      this.handleClickOutside(event);\r\n    }, 50);\r\n  }\r\n\r\n  private handleClickOutside(event: Event) {\r\n    // Check if datepicker is open\r\n    if (\r\n      !this.dateRangePicker ||\r\n      !this.dateRangePicker.isOpen ||\r\n      !this.dateRangePicker.isOpen()\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (this.isSelecting) {\r\n      return;\r\n    }\r\n\r\n    const target = event.target as HTMLElement;\r\n\r\n    if (!target) return;\r\n\r\n    if (\r\n      target.closest('.btn') ||\r\n      target.classList.contains('feather') ||\r\n      target.classList.contains('icon-calendar')\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (\r\n      target.tagName === 'INPUT' &&\r\n      target.getAttribute('name') === 'daterange'\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    const datepickerElement = document.querySelector('ngb-datepicker');\r\n    if (datepickerElement && datepickerElement.contains(target)) {\r\n      return;\r\n    }\r\n\r\n    this.dateRangePicker.close();\r\n  }\r\n\r\n  clearDateRange() {\r\n    this.fromDate = null;\r\n    this.toDate = null;\r\n    this.isSelecting = false;\r\n    this.updateDateRange();\r\n  }\r\n\r\n  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {\r\n    const parsed = this.formatter.parse(input);\r\n    return parsed && this.calendar.isValid(NgbDate.from(parsed))\r\n      ? NgbDate.from(parsed)\r\n      : currentValue;\r\n  }\r\n\r\n  isHovered(date: NgbDate) {\r\n    return (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      this.hoveredDate &&\r\n      date.after(this.fromDate) &&\r\n      date.before(this.hoveredDate)\r\n    );\r\n  }\r\n\r\n  isInside(date: NgbDate) {\r\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\r\n  }\r\n\r\n  isRange(date: NgbDate) {\r\n    return (\r\n      date.equals(this.fromDate) ||\r\n      (this.toDate && date.equals(this.toDate)) ||\r\n      this.isInside(date) ||\r\n      this.isHovered(date)\r\n    );\r\n  }\r\n\r\n  onDateRangeChange() {\r\n    console.log('onDateRangeChange:', this.dateRange);\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  private refreshChildDataTable() {\r\n    // Refresh the league table view component's DataTable\r\n    if (this.leagueTableViewComponent) {\r\n      this.leagueTableViewComponent.refreshDataTable();\r\n    }\r\n  }\r\n\r\n  updateDateRange() {\r\n    if (this.fromDate && this.toDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      const toMoment = moment(\r\n        `${this.toDate.year}-${this.toDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`\r\n      );\r\n\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\r\n      this.dateRangeValue = `${fromMoment.format(\r\n        'MMM DD, YYYY'\r\n      )} - ${toMoment.format('MMM DD, YYYY')}`;\r\n\r\n      this.onDateRangeChange();\r\n    } else if (this.fromDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\r\n    } else {\r\n      this.dateRange.start_date = null;\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = '';\r\n      this.onDateRangeChange();\r\n    }\r\n  }\r\n\r\n  _getTournaments() {\r\n    if (this.seasonId) {\r\n      this._http\r\n        .get<any>(\r\n          `${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`\r\n        )\r\n        .subscribe(\r\n          (data) => {\r\n            this.tournaments = data.data;\r\n          },\r\n          (error) => {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: error.message,\r\n              icon: 'error',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n            });\r\n          }\r\n        );\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Matches Report'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Home'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Tournaments'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Matches Report'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    this._getCurrentSeason();\r\n    this._getClubs();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}