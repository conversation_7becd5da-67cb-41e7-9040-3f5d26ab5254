{"ast": null, "code": "import { environment } from 'environments/environment';\nimport moment from 'moment';\nimport { NgbDate } from '@ng-bootstrap/ng-bootstrap';\nimport Swal from 'sweetalert2';\nimport { LeagueTableViewComponent } from './league-table-view/league-table-view.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"app/services/loading.service\";\nimport * as i5 from \"app/services/registration.service\";\nimport * as i6 from \"app/services/club.service\";\nimport * as i7 from \"@angular/platform-browser\";\nimport * as i8 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i9 from \"app/layout/components/content-header/content-header.component\";\nimport * as i10 from \"./league-table-view/league-table-view.component\";\nimport * as i11 from \"./schedule-view/schedule-view.component\";\nconst _c0 = [\"dateRangePicker\"];\nfunction LeagueReportsComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-league-table-view\");\n  }\n}\nfunction LeagueReportsComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-schedule-view\");\n  }\n}\nexport class LeagueReportsComponent {\n  constructor(route, _router, _http, _trans, renderer, _loadingService, _registrationService, _clubService, _translateService, _titleService, calendar, formatter) {\n    this.route = route;\n    this._router = _router;\n    this._http = _http;\n    this._trans = _trans;\n    this.renderer = renderer;\n    this._loadingService = _loadingService;\n    this._registrationService = _registrationService;\n    this._clubService = _clubService;\n    this._translateService = _translateService;\n    this._titleService = _titleService;\n    this.calendar = calendar;\n    this.formatter = formatter;\n    this.clubId = null;\n    this.tournamentId = null;\n    this.dateRange = {\n      start_date: null,\n      end_date: null\n    };\n    this.dateRangeValue = '';\n    this.hoveredDate = null;\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false; // Track if we're in the middle of selecting range\n    this.matchStatus = 'all';\n    this.matchStatusOptions = [{\n      label: 'All Status',\n      value: 'all'\n    }, {\n      label: 'Upcoming',\n      value: 'upcoming'\n    }, {\n      label: 'Active',\n      value: 'active'\n    }, {\n      label: 'Completed',\n      value: 'completed'\n    }, {\n      label: 'Cancelled',\n      value: 'cancelled'\n    }];\n    this._titleService.setTitle('Matches Report');\n  }\n  _getCurrentSeason() {\n    this._loadingService.show();\n    this._registrationService.getAllSeasonActive().subscribe(data => {\n      this.seasons = data;\n      this.seasonId = this.seasons[0].id;\n      this._getTournaments(); // Fetch tournaments after getting seasons\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    }, () => {\n      this._loadingService.dismiss();\n    });\n  }\n  _getClubs() {\n    this._clubService.getAllClubs().subscribe(res => {\n      this.clubs = res.data;\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  onSelectSeason($event) {\n    return new Promise((resolve, reject) => {\n      this.seasonId = $event;\n      this._getTournaments(); // Fetch tournaments when season changes\n      // Reset tournament selection when season changes\n      this.tournamentId = null;\n      this.refreshChildDataTable();\n      resolve(true);\n    });\n  }\n  onSelectClub($event) {\n    console.log(`onSelectClub: ${$event}`);\n    this.clubId = $event;\n    this.refreshChildDataTable();\n  }\n  onSelectTournament($event) {\n    console.log(`onSelectTournament: ${$event}`);\n    this.tournamentId = $event;\n    this.refreshChildDataTable();\n  }\n  onSelectMatchStatus($event) {\n    console.log(`onSelectMatchStatus: ${$event}`);\n    this.matchStatus = $event;\n    this.refreshChildDataTable();\n  }\n  onDateSelection(date) {\n    if (!this.fromDate && !this.toDate) {\n      console.log('Setting From Date');\n      this.fromDate = date;\n      this.isSelecting = true;\n    } else if (this.fromDate && !this.toDate && date && date.after(this.fromDate)) {\n      this.toDate = date;\n      this.isSelecting = false;\n      this.updateDateRange();\n      setTimeout(() => {\n        if (this.dateRangePicker && this.dateRangePicker.close) {\n          this.dateRangePicker.close();\n        }\n      }, 0);\n    } else {\n      console.log('reset date form');\n      this.toDate = null;\n      this.fromDate = date;\n      this.isSelecting = true;\n    }\n  }\n  openDatePicker() {\n    this.isSelecting = false; // Reset selection state when opening\n    if (this.dateRangePicker) {\n      this.dateRangePicker.open();\n    }\n  }\n  onDocumentClick(event) {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n    this.clickOutsideTimeout = setTimeout(() => {\n      this.handleClickOutside(event);\n    }, 50);\n  }\n  handleClickOutside(event) {\n    // Check if datepicker is open\n    if (!this.dateRangePicker || !this.dateRangePicker.isOpen || !this.dateRangePicker.isOpen()) {\n      return;\n    }\n    if (this.isSelecting) {\n      return;\n    }\n    const target = event.target;\n    if (!target) return;\n    if (target.closest('.btn') || target.classList.contains('feather') || target.classList.contains('icon-calendar')) {\n      return;\n    }\n    if (target.tagName === 'INPUT' && target.getAttribute('name') === 'daterange') {\n      return;\n    }\n    const datepickerElement = document.querySelector('ngb-datepicker');\n    if (datepickerElement && datepickerElement.contains(target)) {\n      return;\n    }\n    this.dateRangePicker.close();\n  }\n  clearDateRange() {\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false;\n    this.updateDateRange();\n  }\n  validateInput(currentValue, input) {\n    const parsed = this.formatter.parse(input);\n    return parsed && this.calendar.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;\n  }\n  isHovered(date) {\n    return this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate);\n  }\n  isInside(date) {\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\n  }\n  isRange(date) {\n    return date.equals(this.fromDate) || this.toDate && date.equals(this.toDate) || this.isInside(date) || this.isHovered(date);\n  }\n  onDateRangeChange() {\n    console.log('onDateRangeChange:', this.dateRange);\n    this.refreshChildDataTable();\n  }\n  refreshChildDataTable() {\n    // Refresh the league table view component's DataTable\n    if (this.leagueTableViewComponent) {\n      this.leagueTableViewComponent.refreshDataTable();\n    }\n  }\n  updateDateRange() {\n    if (this.fromDate && this.toDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      const toMoment = moment(`${this.toDate.year}-${this.toDate.month.toString().padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\n      this.dateRangeValue = `${fromMoment.format('MMM DD, YYYY')} - ${toMoment.format('MMM DD, YYYY')}`;\n      this.onDateRangeChange();\n    } else if (this.fromDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = null;\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\n    } else {\n      this.dateRange.start_date = null;\n      this.dateRange.end_date = null;\n      this.dateRangeValue = '';\n      this.onDateRangeChange();\n    }\n  }\n  _getTournaments() {\n    if (this.seasonId) {\n      this._http.get(`${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`).subscribe(data => {\n        this.tournaments = data.data;\n      }, error => {\n        Swal.fire({\n          title: 'Error',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      });\n    }\n  }\n  ngOnInit() {\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Matches Report'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Home'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Tournaments'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Matches Report'),\n          isLink: false\n        }]\n      }\n    };\n    this._getCurrentSeason();\n    this._getClubs();\n  }\n  ngOnDestroy() {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n  }\n  static #_ = this.ɵfac = function LeagueReportsComponent_Factory(t) {\n    return new (t || LeagueReportsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i4.LoadingService), i0.ɵɵdirectiveInject(i5.RegistrationService), i0.ɵɵdirectiveInject(i6.ClubService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i7.Title), i0.ɵɵdirectiveInject(i8.NgbCalendar), i0.ɵɵdirectiveInject(i8.NgbDateParserFormatter));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LeagueReportsComponent,\n    selectors: [[\"app-league-reports\"]],\n    viewQuery: function LeagueReportsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(LeagueTableViewComponent, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dateRangePicker = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.leagueTableViewComponent = _t.first);\n      }\n    },\n    hostBindings: function LeagueReportsComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function LeagueReportsComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 22,\n    vars: 8,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [\"id\", \"league-reports-page\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", \"m-0\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"league_table\"], [\"ngbNavLink\", \"\"], [1, \"fa-light\", \"fa-table-list\", \"mr-1\"], [\"ngbNavContent\", \"\"], [\"ngbNavItem\", \"schedule_matches\"], [1, \"fa-light\", \"fa-calendar\", \"mr-1\"], [1, \"mt-2\", 3, \"ngbNavOutlet\"]],\n    template: function LeagueReportsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"ul\", 7, 8)(9, \"li\", 9)(10, \"a\", 10);\n        i0.ɵɵelement(11, \"i\", 11);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(14, LeagueReportsComponent_ng_template_14_Template, 1, 0, \"ng-template\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"li\", 13)(16, \"a\", 10);\n        i0.ɵɵelement(17, \"i\", 14);\n        i0.ɵɵtext(18);\n        i0.ɵɵpipe(19, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(20, LeagueReportsComponent_ng_template_20_Template, 1, 0, \"ng-template\", 12);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(21, \"div\", 15);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(8);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(10);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 4, \"Table View\"), \" \");\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 6, \"Schedule View\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngbNavOutlet\", _r0);\n      }\n    },\n    dependencies: [i8.NgbNavContent, i8.NgbNav, i8.NgbNavItem, i8.NgbNavLink, i8.NgbNavOutlet, i9.ContentHeaderComponent, i10.LeagueTableViewComponent, i11.ScheduleViewComponent, i3.TranslatePipe],\n    styles: [\".min-w-max[_ngcontent-%COMP%] {\\n  min-width: max-content;\\n}\\n\\n.custom-day[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 0.185rem 0.25rem;\\n  border-radius: 0.25rem;\\n  display: inline-block;\\n  width: 2rem;\\n  cursor: pointer;\\n}\\n\\n.custom-day[_ngcontent-%COMP%]:hover, .custom-day.focused[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n}\\n\\n.custom-day.range[_ngcontent-%COMP%], .custom-day[_ngcontent-%COMP%]:hover {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.custom-day.faded[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 123, 255, 0.5);\\n  color: white;\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  background: transparent;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvbGVhZ3VlLXJlcG9ydHMvbGVhZ3VlLXJlcG9ydHMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxzQkFBQTtBQUNGOztBQUdBO0VBQ0Usa0JBQUE7RUFDQSx5QkFBQTtFQUNBLHNCQUFBO0VBQ0EscUJBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQTtBQUFGOztBQUdBOztFQUVFLHlCQUFBO0FBQUY7O0FBR0E7O0VBRUUseUJBQUE7RUFDQSxZQUFBO0FBQUY7O0FBR0E7RUFDRSx3Q0FBQTtFQUNBLFlBQUE7QUFBRjs7QUFHQTtFQUNFLHVCQUFBO0FBQUYiLCJzb3VyY2VzQ29udGVudCI6WyIubWluLXctbWF4IHtcclxuICBtaW4td2lkdGg6IG1heC1jb250ZW50O1xyXG59XHJcblxyXG4vLyBEYXRlIHJhbmdlIHBpY2tlciBzdHlsZXNcclxuLmN1c3RvbS1kYXkge1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBwYWRkaW5nOiAwLjE4NXJlbSAwLjI1cmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XHJcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gIHdpZHRoOiAycmVtO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuLmN1c3RvbS1kYXk6aG92ZXIsXHJcbi5jdXN0b20tZGF5LmZvY3VzZWQge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNlOWVjZWY7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF5LnJhbmdlLFxyXG4uY3VzdG9tLWRheTpob3ZlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzAwN2JmZjtcclxuICBjb2xvcjogd2hpdGU7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF5LmZhZGVkIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDEyMywgMjU1LCAwLjUpO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxufVxyXG5cclxuLm5hdi1saW5rIHtcclxuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAYA,SAASA,WAAW,QAAQ,0BAA0B;AAGtD,OAAOC,MAAM,MAAM,QAAQ;AAE3B,SAAsBC,OAAO,QAAgC,4BAA4B;AACzF,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,wBAAwB,QAAQ,iDAAiD;;;;;;;;;;;;;;;;ICFxEC,EAAA,CAAAC,SAAA,4BAA+C;;;;;IAS/CD,EAAA,CAAAC,SAAA,wBAAuC;;;ADAzD,OAAM,MAAOC,sBAAsB;EA6BjCC,YACUC,KAAqB,EACtBC,OAAe,EACfC,KAAiB,EACjBC,MAAwB,EACxBC,QAAmB,EACnBC,eAA+B,EAC/BC,oBAAyC,EACzCC,YAAyB,EACzBC,iBAAmC,EACnCC,aAAoB,EACnBC,QAAqB,EACtBC,SAAiC;IAXhC,KAAAX,KAAK,GAALA,KAAK;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,QAAQ,GAARA,QAAQ;IACT,KAAAC,SAAS,GAATA,SAAS;IAnCX,KAAAC,MAAM,GAAQ,IAAI;IAClB,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,SAAS,GAAQ;MAAEC,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAI,CAAE;IACrD,KAAAC,cAAc,GAAW,EAAE;IAClC,KAAAC,WAAW,GAAmB,IAAI;IAClC,KAAAC,QAAQ,GAAmB,IAAI;IAC/B,KAAAC,MAAM,GAAmB,IAAI;IAC7B,KAAAC,WAAW,GAAY,KAAK,CAAC,CAAC;IAEvB,KAAAC,WAAW,GAAW,KAAK;IAMlC,KAAAC,kBAAkB,GAAG,CACnB;MAAEC,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAK,CAAE,EACrC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,EACxC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,CAC3C;IAgBC,IAAI,CAAChB,aAAa,CAACiB,QAAQ,CAAC,gBAAgB,CAAC;EAC/C;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACtB,eAAe,CAACuB,IAAI,EAAE;IAC3B,IAAI,CAACtB,oBAAoB,CAACuB,kBAAkB,EAAE,CAACC,SAAS,CACrDC,IAAI,IAAI;MACP,IAAI,CAACC,OAAO,GAAGD,IAAI;MACnB,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACD,OAAO,CAAC,CAAC,CAAC,CAACE,EAAE;MAClC,IAAI,CAACC,eAAe,EAAE,CAAC,CAAC;IAC1B,CAAC,EACAC,KAAK,IAAI;MACR1C,IAAI,CAAC2C,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAAClC,iBAAiB,CAACmC,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,EACD,MAAK;MACH,IAAI,CAACtC,eAAe,CAACuC,OAAO,EAAE;IAChC,CAAC,CACF;EACH;EAEAC,SAASA,CAAA;IACP,IAAI,CAACtC,YAAY,CAACuC,WAAW,EAAE,CAAChB,SAAS,CACtCiB,GAAG,IAAI;MACN,IAAI,CAACC,KAAK,GAAGD,GAAG,CAAChB,IAAI;IACvB,CAAC,EACAK,KAAK,IAAI;MACR1C,IAAI,CAAC2C,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAAClC,iBAAiB,CAACmC,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEAM,cAAcA,CAACC,MAAW;IACxB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAACpB,QAAQ,GAAGiB,MAAM;MACtB,IAAI,CAACf,eAAe,EAAE,CAAC,CAAC;MACxB;MACA,IAAI,CAACtB,YAAY,GAAG,IAAI;MACxB,IAAI,CAACyC,qBAAqB,EAAE;MAC5BF,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;EACJ;EAEAG,YAAYA,CAACL,MAAW;IACtBM,OAAO,CAACC,GAAG,CAAC,iBAAiBP,MAAM,EAAE,CAAC;IACtC,IAAI,CAACtC,MAAM,GAAGsC,MAAM;IACpB,IAAI,CAACI,qBAAqB,EAAE;EAC9B;EAEAI,kBAAkBA,CAACR,MAAW;IAC5BM,OAAO,CAACC,GAAG,CAAC,uBAAuBP,MAAM,EAAE,CAAC;IAC5C,IAAI,CAACrC,YAAY,GAAGqC,MAAM;IAC1B,IAAI,CAACI,qBAAqB,EAAE;EAC9B;EAEAK,mBAAmBA,CAACT,MAAW;IAC7BM,OAAO,CAACC,GAAG,CAAC,wBAAwBP,MAAM,EAAE,CAAC;IAC7C,IAAI,CAAC5B,WAAW,GAAG4B,MAAM;IACzB,IAAI,CAACI,qBAAqB,EAAE;EAC9B;EAEAM,eAAeA,CAACC,IAAa;IAE3B,IAAI,CAAC,IAAI,CAAC1C,QAAQ,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MAClCoC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,IAAI,CAACtC,QAAQ,GAAG0C,IAAI;MACpB,IAAI,CAACxC,WAAW,GAAG,IAAI;KACxB,MAAM,IACL,IAAI,CAACF,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZyC,IAAI,IACJA,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC3C,QAAQ,CAAC,EACzB;MACA,IAAI,CAACC,MAAM,GAAGyC,IAAI;MAClB,IAAI,CAACxC,WAAW,GAAG,KAAK;MACxB,IAAI,CAAC0C,eAAe,EAAE;MACtBC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACC,eAAe,IAAI,IAAI,CAACA,eAAe,CAACC,KAAK,EAAE;UACtD,IAAI,CAACD,eAAe,CAACC,KAAK,EAAE;;MAEhC,CAAC,EAAE,CAAC,CAAC;KACN,MAAM;MACLV,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B,IAAI,CAACrC,MAAM,GAAG,IAAI;MAClB,IAAI,CAACD,QAAQ,GAAG0C,IAAI;MACpB,IAAI,CAACxC,WAAW,GAAG,IAAI;;EAE3B;EAEA8C,cAAcA,CAAA;IACZ,IAAI,CAAC9C,WAAW,GAAG,KAAK,CAAC,CAAC;IAC1B,IAAI,IAAI,CAAC4C,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACG,IAAI,EAAE;;EAE/B;EAGAC,eAAeA,CAACC,KAAY;IAC1B,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;IAGxC,IAAI,CAACA,mBAAmB,GAAGP,UAAU,CAAC,MAAK;MACzC,IAAI,CAACS,kBAAkB,CAACH,KAAK,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC;EACR;EAEQG,kBAAkBA,CAACH,KAAY;IACrC;IACA,IACE,CAAC,IAAI,CAACL,eAAe,IACrB,CAAC,IAAI,CAACA,eAAe,CAACS,MAAM,IAC5B,CAAC,IAAI,CAACT,eAAe,CAACS,MAAM,EAAE,EAC9B;MACA;;IAGF,IAAI,IAAI,CAACrD,WAAW,EAAE;MACpB;;IAGF,MAAMsD,MAAM,GAAGL,KAAK,CAACK,MAAqB;IAE1C,IAAI,CAACA,MAAM,EAAE;IAEb,IACEA,MAAM,CAACC,OAAO,CAAC,MAAM,CAAC,IACtBD,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,SAAS,CAAC,IACpCH,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,EAC1C;MACA;;IAGF,IACEH,MAAM,CAACI,OAAO,KAAK,OAAO,IAC1BJ,MAAM,CAACK,YAAY,CAAC,MAAM,CAAC,KAAK,WAAW,EAC3C;MACA;;IAGF,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;IAClE,IAAIF,iBAAiB,IAAIA,iBAAiB,CAACH,QAAQ,CAACH,MAAM,CAAC,EAAE;MAC3D;;IAGF,IAAI,CAACV,eAAe,CAACC,KAAK,EAAE;EAC9B;EAEAkB,cAAcA,CAAA;IACZ,IAAI,CAACjE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC0C,eAAe,EAAE;EACxB;EAEAsB,aAAaA,CAACC,YAA4B,EAAEC,KAAa;IACvD,MAAMC,MAAM,GAAG,IAAI,CAAC7E,SAAS,CAAC8E,KAAK,CAACF,KAAK,CAAC;IAC1C,OAAOC,MAAM,IAAI,IAAI,CAAC9E,QAAQ,CAACgF,OAAO,CAACjG,OAAO,CAACkG,IAAI,CAACH,MAAM,CAAC,CAAC,GACxD/F,OAAO,CAACkG,IAAI,CAACH,MAAM,CAAC,GACpBF,YAAY;EAClB;EAEAM,SAASA,CAAC/B,IAAa;IACrB,OACE,IAAI,CAAC1C,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZ,IAAI,CAACF,WAAW,IAChB2C,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC3C,QAAQ,CAAC,IACzB0C,IAAI,CAACgC,MAAM,CAAC,IAAI,CAAC3E,WAAW,CAAC;EAEjC;EAEA4E,QAAQA,CAACjC,IAAa;IACpB,OAAO,IAAI,CAACzC,MAAM,IAAIyC,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC3C,QAAQ,CAAC,IAAI0C,IAAI,CAACgC,MAAM,CAAC,IAAI,CAACzE,MAAM,CAAC;EAC7E;EAEA2E,OAAOA,CAAClC,IAAa;IACnB,OACEA,IAAI,CAACmC,MAAM,CAAC,IAAI,CAAC7E,QAAQ,CAAC,IACzB,IAAI,CAACC,MAAM,IAAIyC,IAAI,CAACmC,MAAM,CAAC,IAAI,CAAC5E,MAAM,CAAE,IACzC,IAAI,CAAC0E,QAAQ,CAACjC,IAAI,CAAC,IACnB,IAAI,CAAC+B,SAAS,CAAC/B,IAAI,CAAC;EAExB;EAEAoC,iBAAiBA,CAAA;IACfzC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC3C,SAAS,CAAC;IACjD,IAAI,CAACwC,qBAAqB,EAAE;EAC9B;EAEQA,qBAAqBA,CAAA;IAC3B;IACA,IAAI,IAAI,CAAC4C,wBAAwB,EAAE;MACjC,IAAI,CAACA,wBAAwB,CAACC,gBAAgB,EAAE;;EAEpD;EAEApC,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC5C,QAAQ,IAAI,IAAI,CAACC,MAAM,EAAE;MAChC,MAAMgF,UAAU,GAAG5G,MAAM,CACvB,GAAG,IAAI,CAAC2B,QAAQ,CAACkF,IAAI,IAAI,IAAI,CAAClF,QAAQ,CAACmF,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAACrF,QAAQ,CAACsF,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,MAAME,QAAQ,GAAGlH,MAAM,CACrB,GAAG,IAAI,CAAC4B,MAAM,CAACiF,IAAI,IAAI,IAAI,CAACjF,MAAM,CAACkF,KAAK,CACrCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAACpF,MAAM,CAACqF,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACrE;MAED,IAAI,CAAC1F,SAAS,CAACC,UAAU,GAAGqF,UAAU,CAACO,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAAC7F,SAAS,CAACE,QAAQ,GAAG0F,QAAQ,CAACC,MAAM,CAAC,YAAY,CAAC;MACvD,IAAI,CAAC1F,cAAc,GAAG,GAAGmF,UAAU,CAACO,MAAM,CACxC,cAAc,CACf,MAAMD,QAAQ,CAACC,MAAM,CAAC,cAAc,CAAC,EAAE;MAExC,IAAI,CAACV,iBAAiB,EAAE;KACzB,MAAM,IAAI,IAAI,CAAC9E,QAAQ,EAAE;MACxB,MAAMiF,UAAU,GAAG5G,MAAM,CACvB,GAAG,IAAI,CAAC2B,QAAQ,CAACkF,IAAI,IAAI,IAAI,CAAClF,QAAQ,CAACmF,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAACrF,QAAQ,CAACsF,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,IAAI,CAAC1F,SAAS,CAACC,UAAU,GAAGqF,UAAU,CAACO,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAAC7F,SAAS,CAACE,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAGmF,UAAU,CAACO,MAAM,CAAC,cAAc,CAAC;KACxD,MAAM;MACL,IAAI,CAAC7F,SAAS,CAACC,UAAU,GAAG,IAAI;MAChC,IAAI,CAACD,SAAS,CAACE,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACgF,iBAAiB,EAAE;;EAE5B;EAEA9D,eAAeA,CAAA;IACb,IAAI,IAAI,CAACF,QAAQ,EAAE;MACjB,IAAI,CAAC/B,KAAK,CACP0G,GAAG,CACF,GAAGrH,WAAW,CAACsH,MAAM,YAAY,IAAI,CAAC5E,QAAQ,+BAA+B,CAC9E,CACAH,SAAS,CACPC,IAAI,IAAI;QACP,IAAI,CAAC+E,WAAW,GAAG/E,IAAI,CAACA,IAAI;MAC9B,CAAC,EACAK,KAAK,IAAI;QACR1C,IAAI,CAAC2C,IAAI,CAAC;UACRC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAEH,KAAK,CAACI,OAAO;UACnBC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAAClC,iBAAiB,CAACmC,OAAO,CAAC,IAAI;SACvD,CAAC;MACJ,CAAC,CACF;;EAEP;EAEAoE,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAAC9G,MAAM,CAACwC,OAAO,CAAC,gBAAgB,CAAC;MAClDuE,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,IAAI,CAACnH,MAAM,CAACwC,OAAO,CAAC,MAAM,CAAC;UACjC4E,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACnH,MAAM,CAACwC,OAAO,CAAC,aAAa,CAAC;UACxC4E,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACnH,MAAM,CAACwC,OAAO,CAAC,gBAAgB,CAAC;UAC3C4E,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAAC5F,iBAAiB,EAAE;IACxB,IAAI,CAACkB,SAAS,EAAE;EAClB;EAEA2E,WAAWA,CAAA;IACT,IAAI,IAAI,CAACjD,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;EAE1C;EAAC,QAAAkD,CAAA;qBAnVU3H,sBAAsB,EAAAF,EAAA,CAAA8H,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAhI,EAAA,CAAA8H,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAjI,EAAA,CAAA8H,iBAAA,CAAAI,EAAA,CAAAC,UAAA,GAAAnI,EAAA,CAAA8H,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAArI,EAAA,CAAA8H,iBAAA,CAAA9H,EAAA,CAAAsI,SAAA,GAAAtI,EAAA,CAAA8H,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAxI,EAAA,CAAA8H,iBAAA,CAAAW,EAAA,CAAAC,mBAAA,GAAA1I,EAAA,CAAA8H,iBAAA,CAAAa,EAAA,CAAAC,WAAA,GAAA5I,EAAA,CAAA8H,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAArI,EAAA,CAAA8H,iBAAA,CAAAe,EAAA,CAAAC,KAAA,GAAA9I,EAAA,CAAA8H,iBAAA,CAAAiB,EAAA,CAAAC,WAAA,GAAAhJ,EAAA,CAAA8H,iBAAA,CAAAiB,EAAA,CAAAE,sBAAA;EAAA;EAAA,QAAAC,EAAA;UAAtBhJ,sBAAsB;IAAAiJ,SAAA;IAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;uBAGtBvJ,wBAAwB;;;;;;;;;;;iBAHxBwJ,GAAA,CAAA9E,eAAA,CAAAnB,MAAA,CAAuB;QAAA,UAAAtD,EAAA,CAAAwJ,iBAAA;;;;;;;;QC1BpCxJ,EAAA,CAAAyJ,cAAA,aAA+C;QAG3CzJ,EAAA,CAAAC,SAAA,4BAAyE;QAEzED,EAAA,CAAAyJ,cAAA,iBAAkC;QAQpBzJ,EAAA,CAAAC,SAAA,aAA2C;QAC3CD,EAAA,CAAA0J,MAAA,IACF;;QAAA1J,EAAA,CAAA2J,YAAA,EAAI;QACJ3J,EAAA,CAAA4J,UAAA,KAAAC,8CAAA,0BAEc;QAChB7J,EAAA,CAAA2J,YAAA,EAAK;QACL3J,EAAA,CAAAyJ,cAAA,cAAkC;QAE9BzJ,EAAA,CAAAC,SAAA,aAAyC;QACzCD,EAAA,CAAA0J,MAAA,IACF;;QAAA1J,EAAA,CAAA2J,YAAA,EAAI;QACJ3J,EAAA,CAAA4J,UAAA,KAAAE,8CAAA,0BAEc;QAChB9J,EAAA,CAAA2J,YAAA,EAAK;QAGT3J,EAAA,CAAAC,SAAA,eAA6C;QAC/CD,EAAA,CAAA2J,YAAA,EAAM;;;;QA7BU3J,EAAA,CAAA+J,SAAA,GAA+B;QAA/B/J,EAAA,CAAAgK,UAAA,kBAAAT,GAAA,CAAAnC,aAAA,CAA+B;QAWrCpH,EAAA,CAAA+J,SAAA,IACF;QADE/J,EAAA,CAAAiK,kBAAA,MAAAjK,EAAA,CAAAkK,WAAA,2BACF;QAQElK,EAAA,CAAA+J,SAAA,GACF;QADE/J,EAAA,CAAAiK,kBAAA,MAAAjK,EAAA,CAAAkK,WAAA,8BACF;QAODlK,EAAA,CAAA+J,SAAA,GAAoB;QAApB/J,EAAA,CAAAgK,UAAA,iBAAAG,GAAA,CAAoB", "names": ["environment", "moment", "NgbDate", "<PERSON><PERSON>", "LeagueTableViewComponent", "i0", "ɵɵelement", "LeagueReportsComponent", "constructor", "route", "_router", "_http", "_trans", "renderer", "_loadingService", "_registrationService", "_clubService", "_translateService", "_titleService", "calendar", "formatter", "clubId", "tournamentId", "date<PERSON><PERSON><PERSON>", "start_date", "end_date", "dateRangeValue", "hoveredDate", "fromDate", "toDate", "isSelecting", "matchStatus", "matchStatusOptions", "label", "value", "setTitle", "_getCurrentSeason", "show", "getAllSeasonActive", "subscribe", "data", "seasons", "seasonId", "id", "_getTournaments", "error", "fire", "title", "text", "message", "icon", "confirmButtonText", "instant", "dismiss", "_getClubs", "getAllClubs", "res", "clubs", "onSelectSeason", "$event", "Promise", "resolve", "reject", "refreshChildDataTable", "onSelectClub", "console", "log", "onSelectTournament", "onSelectMatchStatus", "onDateSelection", "date", "after", "updateDateRange", "setTimeout", "dateRangePicker", "close", "openDatePicker", "open", "onDocumentClick", "event", "clickOutsideTimeout", "clearTimeout", "handleClickOutside", "isOpen", "target", "closest", "classList", "contains", "tagName", "getAttribute", "datepickerElement", "document", "querySelector", "clearDateRange", "validateInput", "currentValue", "input", "parsed", "parse", "<PERSON><PERSON><PERSON><PERSON>", "from", "isHovered", "before", "isInside", "isRange", "equals", "onDateRangeChange", "leagueTableViewComponent", "refreshDataTable", "fromMoment", "year", "month", "toString", "padStart", "day", "toMoment", "format", "get", "apiUrl", "tournaments", "ngOnInit", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "type", "links", "name", "isLink", "ngOnDestroy", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "HttpClient", "i3", "TranslateService", "Renderer2", "i4", "LoadingService", "i5", "RegistrationService", "i6", "ClubService", "i7", "Title", "i8", "NgbCalendar", "NgbDateParserFormatter", "_2", "selectors", "viewQuery", "LeagueReportsComponent_Query", "rf", "ctx", "ɵɵresolveDocument", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "LeagueReportsComponent_ng_template_14_Template", "LeagueReportsComponent_ng_template_20_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "_r0"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport {\r\n  Component,\r\n  OnInit,\r\n  Renderer2,\r\n  ViewChild,\r\n  HostListener,\r\n  OnD<PERSON>roy\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { environment } from 'environments/environment';\r\nimport { RegistrationService } from 'app/services/registration.service';\r\nimport { ClubService } from 'app/services/club.service';\r\nimport moment from 'moment';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { NgbCalendar, NgbDate, NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';\r\nimport Swal from 'sweetalert2';\r\nimport { LeagueTableViewComponent } from './league-table-view/league-table-view.component';\r\n\r\n@Component({\r\n  selector: 'app-league-reports',\r\n  templateUrl: './league-reports.component.html',\r\n  styleUrls: ['./league-reports.component.scss'],\r\n})\r\nexport class LeagueReportsComponent implements OnInit, OnD<PERSON>roy\r\n{\r\n  @ViewChild('dateRangePicker', { static: false }) dateRangePicker: any;\r\n  @ViewChild(LeagueTableViewComponent, { static: false }) leagueTableViewComponent: LeagueTableViewComponent;\r\n\r\n  public seasonId: any;\r\n  public clubId: any = null;\r\n  public tournamentId: any = null;\r\n  public dateRange: any = { start_date: null, end_date: null };\r\n  public dateRangeValue: string = '';\r\n  hoveredDate: NgbDate | null = null;\r\n  fromDate: NgbDate | null = null;\r\n  toDate: NgbDate | null = null;\r\n  isSelecting: boolean = false; // Track if we're in the middle of selecting range\r\n  private clickOutsideTimeout: any;\r\n  public matchStatus: string = 'all';\r\n  public contentHeader: object;\r\n  public seasons: any[];\r\n  public clubs: any[];\r\n  public tournaments: any[];\r\n\r\n  matchStatusOptions = [\r\n    { label: 'All Status', value: 'all' },\r\n    { label: 'Upcoming', value: 'upcoming' },\r\n    { label: 'Active', value: 'active' },\r\n    { label: 'Completed', value: 'completed' },\r\n    { label: 'Cancelled', value: 'cancelled' },\r\n  ];\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    public _router: Router,\r\n    public _http: HttpClient,\r\n    public _trans: TranslateService,\r\n    public renderer: Renderer2,\r\n    public _loadingService: LoadingService,\r\n    public _registrationService: RegistrationService,\r\n    public _clubService: ClubService,\r\n    public _translateService: TranslateService,\r\n    public _titleService: Title,\r\n    private calendar: NgbCalendar,\r\n    public formatter: NgbDateParserFormatter\r\n  ) {\r\n    this._titleService.setTitle('Matches Report');\r\n  }\r\n\r\n  _getCurrentSeason() {\r\n    this._loadingService.show();\r\n    this._registrationService.getAllSeasonActive().subscribe(\r\n      (data) => {\r\n        this.seasons = data;\r\n        this.seasonId = this.seasons[0].id;\r\n        this._getTournaments(); // Fetch tournaments after getting seasons\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      },\r\n      () => {\r\n        this._loadingService.dismiss();\r\n      }\r\n    );\r\n  }\r\n\r\n  _getClubs() {\r\n    this._clubService.getAllClubs().subscribe(\r\n      (res) => {\r\n        this.clubs = res.data;\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  onSelectSeason($event: any) {\r\n    return new Promise((resolve, reject) => {\r\n      this.seasonId = $event;\r\n      this._getTournaments(); // Fetch tournaments when season changes\r\n      // Reset tournament selection when season changes\r\n      this.tournamentId = null;\r\n      this.refreshChildDataTable();\r\n      resolve(true);\r\n    });\r\n  }\r\n\r\n  onSelectClub($event: any) {\r\n    console.log(`onSelectClub: ${$event}`);\r\n    this.clubId = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onSelectTournament($event: any) {\r\n    console.log(`onSelectTournament: ${$event}`);\r\n    this.tournamentId = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onSelectMatchStatus($event: any) {\r\n    console.log(`onSelectMatchStatus: ${$event}`);\r\n    this.matchStatus = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onDateSelection(date: NgbDate) {\r\n\r\n    if (!this.fromDate && !this.toDate) {\r\n      console.log('Setting From Date');\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    } else if (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      date &&\r\n      date.after(this.fromDate)\r\n    ) {\r\n      this.toDate = date;\r\n      this.isSelecting = false;\r\n      this.updateDateRange();\r\n      setTimeout(() => {\r\n        if (this.dateRangePicker && this.dateRangePicker.close) {\r\n          this.dateRangePicker.close();\r\n        }\r\n      }, 0);\r\n    } else {\r\n      console.log('reset date form');\r\n      this.toDate = null;\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    }\r\n  }\r\n\r\n  openDatePicker() {\r\n    this.isSelecting = false; // Reset selection state when opening\r\n    if (this.dateRangePicker) {\r\n      this.dateRangePicker.open();\r\n    }\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event) {\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n\r\n    this.clickOutsideTimeout = setTimeout(() => {\r\n      this.handleClickOutside(event);\r\n    }, 50);\r\n  }\r\n\r\n  private handleClickOutside(event: Event) {\r\n    // Check if datepicker is open\r\n    if (\r\n      !this.dateRangePicker ||\r\n      !this.dateRangePicker.isOpen ||\r\n      !this.dateRangePicker.isOpen()\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (this.isSelecting) {\r\n      return;\r\n    }\r\n\r\n    const target = event.target as HTMLElement;\r\n\r\n    if (!target) return;\r\n\r\n    if (\r\n      target.closest('.btn') ||\r\n      target.classList.contains('feather') ||\r\n      target.classList.contains('icon-calendar')\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (\r\n      target.tagName === 'INPUT' &&\r\n      target.getAttribute('name') === 'daterange'\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    const datepickerElement = document.querySelector('ngb-datepicker');\r\n    if (datepickerElement && datepickerElement.contains(target)) {\r\n      return;\r\n    }\r\n\r\n    this.dateRangePicker.close();\r\n  }\r\n\r\n  clearDateRange() {\r\n    this.fromDate = null;\r\n    this.toDate = null;\r\n    this.isSelecting = false;\r\n    this.updateDateRange();\r\n  }\r\n\r\n  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {\r\n    const parsed = this.formatter.parse(input);\r\n    return parsed && this.calendar.isValid(NgbDate.from(parsed))\r\n      ? NgbDate.from(parsed)\r\n      : currentValue;\r\n  }\r\n\r\n  isHovered(date: NgbDate) {\r\n    return (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      this.hoveredDate &&\r\n      date.after(this.fromDate) &&\r\n      date.before(this.hoveredDate)\r\n    );\r\n  }\r\n\r\n  isInside(date: NgbDate) {\r\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\r\n  }\r\n\r\n  isRange(date: NgbDate) {\r\n    return (\r\n      date.equals(this.fromDate) ||\r\n      (this.toDate && date.equals(this.toDate)) ||\r\n      this.isInside(date) ||\r\n      this.isHovered(date)\r\n    );\r\n  }\r\n\r\n  onDateRangeChange() {\r\n    console.log('onDateRangeChange:', this.dateRange);\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  private refreshChildDataTable() {\r\n    // Refresh the league table view component's DataTable\r\n    if (this.leagueTableViewComponent) {\r\n      this.leagueTableViewComponent.refreshDataTable();\r\n    }\r\n  }\r\n\r\n  updateDateRange() {\r\n    if (this.fromDate && this.toDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      const toMoment = moment(\r\n        `${this.toDate.year}-${this.toDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`\r\n      );\r\n\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\r\n      this.dateRangeValue = `${fromMoment.format(\r\n        'MMM DD, YYYY'\r\n      )} - ${toMoment.format('MMM DD, YYYY')}`;\r\n\r\n      this.onDateRangeChange();\r\n    } else if (this.fromDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\r\n    } else {\r\n      this.dateRange.start_date = null;\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = '';\r\n      this.onDateRangeChange();\r\n    }\r\n  }\r\n\r\n  _getTournaments() {\r\n    if (this.seasonId) {\r\n      this._http\r\n        .get<any>(\r\n          `${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`\r\n        )\r\n        .subscribe(\r\n          (data) => {\r\n            this.tournaments = data.data;\r\n          },\r\n          (error) => {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: error.message,\r\n              icon: 'error',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n            });\r\n          }\r\n        );\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Matches Report'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Home'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Tournaments'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Matches Report'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    this._getCurrentSeason();\r\n    this._getClubs();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n  }\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n    <section id=\"league-reports-page\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <!-- Tabs Section -->\r\n          <div class=\"card\">\r\n            <ul ngbNav #nav=\"ngbNav\" class=\"nav-tabs m-0\">\r\n              <li ngbNavItem=\"league_table\">\r\n                <a ngbNavLink>\r\n                  <i class=\"fa-light fa-table-list mr-1\"></i>\r\n                  {{ 'Table View' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <app-league-table-view></app-league-table-view>\r\n                </ng-template>\r\n              </li>\r\n              <li ngbNavItem=\"schedule_matches\">\r\n                <a ngbNavLink>\r\n                  <i class=\"fa-light fa-calendar mr-1\"></i>\r\n                  {{ 'Schedule View' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <app-schedule-view></app-schedule-view>\r\n                </ng-template>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div [ngbNavOutlet]=\"nav\" class=\"mt-2\"></div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</div>\r\n\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}