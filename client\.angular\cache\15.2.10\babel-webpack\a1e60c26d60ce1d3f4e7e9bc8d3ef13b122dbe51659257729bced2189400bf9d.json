{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/platform-browser\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"app/layout/components/content-header/content-header.component\";\nimport * as i5 from \"./league-table-view/league-table-view.component\";\nimport * as i6 from \"./schedule-view/schedule-view.component\";\nfunction LeagueReportsComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-league-table-view\");\n  }\n}\nfunction LeagueReportsComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-schedule-view\");\n  }\n}\nexport class LeagueReportsComponent {\n  constructor(_trans, _titleService) {\n    this._trans = _trans;\n    this._titleService = _titleService;\n    this._titleService.setTitle('Matches Report');\n  }\n  _getCurrentSeason() {\n    this._loadingService.show();\n    this._registrationService.getAllSeasonActive().subscribe(data => {\n      this.seasons = data;\n      this.seasonId = this.seasons[0].id;\n      this._getTournaments(); // Fetch tournaments after getting seasons\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    }, () => {\n      this._loadingService.dismiss();\n    });\n  }\n  _getClubs() {\n    this._clubService.getAllClubs().subscribe(res => {\n      this.clubs = res.data;\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  onSelectSeason($event) {\n    return new Promise((resolve, reject) => {\n      this.seasonId = $event;\n      this._getTournaments(); // Fetch tournaments when season changes\n      // Reset tournament selection when season changes\n      this.tournamentId = null;\n      this.refreshChildDataTable();\n      resolve(true);\n    });\n  }\n  onSelectClub($event) {\n    console.log(`onSelectClub: ${$event}`);\n    this.clubId = $event;\n    this.refreshChildDataTable();\n  }\n  onSelectTournament($event) {\n    console.log(`onSelectTournament: ${$event}`);\n    this.tournamentId = $event;\n    this.refreshChildDataTable();\n  }\n  onSelectMatchStatus($event) {\n    console.log(`onSelectMatchStatus: ${$event}`);\n    this.matchStatus = $event;\n    this.refreshChildDataTable();\n  }\n  onDateSelection(date) {\n    if (!this.fromDate && !this.toDate) {\n      console.log('Setting From Date');\n      this.fromDate = date;\n      this.isSelecting = true;\n    } else if (this.fromDate && !this.toDate && date && date.after(this.fromDate)) {\n      this.toDate = date;\n      this.isSelecting = false;\n      this.updateDateRange();\n      setTimeout(() => {\n        if (this.dateRangePicker && this.dateRangePicker.close) {\n          this.dateRangePicker.close();\n        }\n      }, 0);\n    } else {\n      console.log('reset date form');\n      this.toDate = null;\n      this.fromDate = date;\n      this.isSelecting = true;\n    }\n  }\n  openDatePicker() {\n    this.isSelecting = false; // Reset selection state when opening\n    if (this.dateRangePicker) {\n      this.dateRangePicker.open();\n    }\n  }\n  onDocumentClick(event) {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n    this.clickOutsideTimeout = setTimeout(() => {\n      this.handleClickOutside(event);\n    }, 50);\n  }\n  handleClickOutside(event) {\n    // Check if datepicker is open\n    if (!this.dateRangePicker || !this.dateRangePicker.isOpen || !this.dateRangePicker.isOpen()) {\n      return;\n    }\n    if (this.isSelecting) {\n      return;\n    }\n    const target = event.target;\n    if (!target) return;\n    if (target.closest('.btn') || target.classList.contains('feather') || target.classList.contains('icon-calendar')) {\n      return;\n    }\n    if (target.tagName === 'INPUT' && target.getAttribute('name') === 'daterange') {\n      return;\n    }\n    const datepickerElement = document.querySelector('ngb-datepicker');\n    if (datepickerElement && datepickerElement.contains(target)) {\n      return;\n    }\n    this.dateRangePicker.close();\n  }\n  clearDateRange() {\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false;\n    this.updateDateRange();\n  }\n  validateInput(currentValue, input) {\n    const parsed = this.formatter.parse(input);\n    return parsed && this.calendar.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;\n  }\n  isHovered(date) {\n    return this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate);\n  }\n  isInside(date) {\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\n  }\n  isRange(date) {\n    return date.equals(this.fromDate) || this.toDate && date.equals(this.toDate) || this.isInside(date) || this.isHovered(date);\n  }\n  onDateRangeChange() {\n    console.log('onDateRangeChange:', this.dateRange);\n    this.refreshChildDataTable();\n  }\n  refreshChildDataTable() {\n    // Refresh the league table view component's DataTable\n    if (this.leagueTableViewComponent) {\n      this.leagueTableViewComponent.refreshDataTable();\n    }\n  }\n  updateDateRange() {\n    if (this.fromDate && this.toDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      const toMoment = moment(`${this.toDate.year}-${this.toDate.month.toString().padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\n      this.dateRangeValue = `${fromMoment.format('MMM DD, YYYY')} - ${toMoment.format('MMM DD, YYYY')}`;\n      this.onDateRangeChange();\n    } else if (this.fromDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = null;\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\n    } else {\n      this.dateRange.start_date = null;\n      this.dateRange.end_date = null;\n      this.dateRangeValue = '';\n      this.onDateRangeChange();\n    }\n  }\n  _getTournaments() {\n    if (this.seasonId) {\n      this._http.get(`${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`).subscribe(data => {\n        this.tournaments = data.data;\n      }, error => {\n        Swal.fire({\n          title: 'Error',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      });\n    }\n  }\n  ngOnInit() {\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Matches Report'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Home'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Tournaments'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Matches Report'),\n          isLink: false\n        }]\n      }\n    };\n    this._getCurrentSeason();\n    this._getClubs();\n  }\n  ngOnDestroy() {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n  }\n  static #_ = this.ɵfac = function LeagueReportsComponent_Factory(t) {\n    return new (t || LeagueReportsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.Title));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LeagueReportsComponent,\n    selectors: [[\"app-league-reports\"]],\n    decls: 22,\n    vars: 8,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [\"id\", \"league-reports-page\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", \"m-0\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"league_table\"], [\"ngbNavLink\", \"\"], [1, \"fa-light\", \"fa-table-list\", \"mr-1\"], [\"ngbNavContent\", \"\"], [\"ngbNavItem\", \"schedule_matches\"], [1, \"fa-light\", \"fa-calendar\", \"mr-1\"], [1, \"mt-2\", 3, \"ngbNavOutlet\"]],\n    template: function LeagueReportsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"ul\", 7, 8)(9, \"li\", 9)(10, \"a\", 10);\n        i0.ɵɵelement(11, \"i\", 11);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(14, LeagueReportsComponent_ng_template_14_Template, 1, 0, \"ng-template\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"li\", 13)(16, \"a\", 10);\n        i0.ɵɵelement(17, \"i\", 14);\n        i0.ɵɵtext(18);\n        i0.ɵɵpipe(19, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(20, LeagueReportsComponent_ng_template_20_Template, 1, 0, \"ng-template\", 12);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(21, \"div\", 15);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(8);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(10);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 4, \"Table View\"), \" \");\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 6, \"Schedule View\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngbNavOutlet\", _r0);\n      }\n    },\n    dependencies: [i3.NgbNavContent, i3.NgbNav, i3.NgbNavItem, i3.NgbNavLink, i3.NgbNavOutlet, i4.ContentHeaderComponent, i5.LeagueTableViewComponent, i6.ScheduleViewComponent, i1.TranslatePipe],\n    styles: [\".min-w-max[_ngcontent-%COMP%] {\\n  min-width: max-content;\\n}\\n\\n.custom-day[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 0.185rem 0.25rem;\\n  border-radius: 0.25rem;\\n  display: inline-block;\\n  width: 2rem;\\n  cursor: pointer;\\n}\\n\\n.custom-day[_ngcontent-%COMP%]:hover, .custom-day.focused[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n}\\n\\n.custom-day.range[_ngcontent-%COMP%], .custom-day[_ngcontent-%COMP%]:hover {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.custom-day.faded[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 123, 255, 0.5);\\n  color: white;\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  background: transparent;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvbGVhZ3VlLXJlcG9ydHMvbGVhZ3VlLXJlcG9ydHMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxzQkFBQTtBQUNGOztBQUdBO0VBQ0Usa0JBQUE7RUFDQSx5QkFBQTtFQUNBLHNCQUFBO0VBQ0EscUJBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQTtBQUFGOztBQUdBOztFQUVFLHlCQUFBO0FBQUY7O0FBR0E7O0VBRUUseUJBQUE7RUFDQSxZQUFBO0FBQUY7O0FBR0E7RUFDRSx3Q0FBQTtFQUNBLFlBQUE7QUFBRjs7QUFHQTtFQUNFLHVCQUFBO0FBQUYiLCJzb3VyY2VzQ29udGVudCI6WyIubWluLXctbWF4IHtcclxuICBtaW4td2lkdGg6IG1heC1jb250ZW50O1xyXG59XHJcblxyXG4vLyBEYXRlIHJhbmdlIHBpY2tlciBzdHlsZXNcclxuLmN1c3RvbS1kYXkge1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBwYWRkaW5nOiAwLjE4NXJlbSAwLjI1cmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XHJcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gIHdpZHRoOiAycmVtO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuLmN1c3RvbS1kYXk6aG92ZXIsXHJcbi5jdXN0b20tZGF5LmZvY3VzZWQge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNlOWVjZWY7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF5LnJhbmdlLFxyXG4uY3VzdG9tLWRheTpob3ZlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzAwN2JmZjtcclxuICBjb2xvcjogd2hpdGU7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF5LmZhZGVkIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDEyMywgMjU1LCAwLjUpO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxufVxyXG5cclxuLm5hdi1saW5rIHtcclxuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}\n__decorate([HostListener('document:click', ['$event'])], LeagueReportsComponent.prototype, \"onDocumentClick\", null);", "map": {"version": 3, "mappings": ";;;;;;;;;;IAiBkBA,EAAA,CAAAC,SAAA,4BAA+C;;;;;IAS/CD,EAAA,CAAAC,SAAA,wBAAuC;;;ACdzD,OAAM,MAAOC,sBAAsB;EAIjCC,YACSC,MAAwB,EACxBC,aAAoB;IADpB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAEpB,IAAI,CAACA,aAAa,CAACC,QAAQ,CAAC,gBAAgB,CAAC;EAC/C;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACC,eAAe,CAACC,IAAI,EAAE;IAC3B,IAAI,CAACC,oBAAoB,CAACC,kBAAkB,EAAE,CAACC,SAAS,CACrDC,IAAI,IAAI;MACP,IAAI,CAACC,OAAO,GAAGD,IAAI;MACnB,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACD,OAAO,CAAC,CAAC,CAAC,CAACE,EAAE;MAClC,IAAI,CAACC,eAAe,EAAE,CAAC,CAAC;IAC1B,CAAC,EACAC,KAAK,IAAI;MACRC,IAAI,CAACC,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEJ,KAAK,CAACK,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACC,iBAAiB,CAACC,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,EACD,MAAK;MACH,IAAI,CAACnB,eAAe,CAACoB,OAAO,EAAE;IAChC,CAAC,CACF;EACH;EAEAC,SAASA,CAAA;IACP,IAAI,CAACC,YAAY,CAACC,WAAW,EAAE,CAACnB,SAAS,CACtCoB,GAAG,IAAI;MACN,IAAI,CAACC,KAAK,GAAGD,GAAG,CAACnB,IAAI;IACvB,CAAC,EACAK,KAAK,IAAI;MACRC,IAAI,CAACC,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEJ,KAAK,CAACK,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAACC,iBAAiB,CAACC,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEAO,cAAcA,CAACC,MAAW;IACxB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAACvB,QAAQ,GAAGoB,MAAM;MACtB,IAAI,CAAClB,eAAe,EAAE,CAAC,CAAC;MACxB;MACA,IAAI,CAACsB,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,qBAAqB,EAAE;MAC5BH,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;EACJ;EAEAI,YAAYA,CAACN,MAAW;IACtBO,OAAO,CAACC,GAAG,CAAC,iBAAiBR,MAAM,EAAE,CAAC;IACtC,IAAI,CAACS,MAAM,GAAGT,MAAM;IACpB,IAAI,CAACK,qBAAqB,EAAE;EAC9B;EAEAK,kBAAkBA,CAACV,MAAW;IAC5BO,OAAO,CAACC,GAAG,CAAC,uBAAuBR,MAAM,EAAE,CAAC;IAC5C,IAAI,CAACI,YAAY,GAAGJ,MAAM;IAC1B,IAAI,CAACK,qBAAqB,EAAE;EAC9B;EAEAM,mBAAmBA,CAACX,MAAW;IAC7BO,OAAO,CAACC,GAAG,CAAC,wBAAwBR,MAAM,EAAE,CAAC;IAC7C,IAAI,CAACY,WAAW,GAAGZ,MAAM;IACzB,IAAI,CAACK,qBAAqB,EAAE;EAC9B;EAEAQ,eAAeA,CAACC,IAAa;IAE3B,IAAI,CAAC,IAAI,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MAClCT,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,IAAI,CAACO,QAAQ,GAAGD,IAAI;MACpB,IAAI,CAACG,WAAW,GAAG,IAAI;KACxB,MAAM,IACL,IAAI,CAACF,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZF,IAAI,IACJA,IAAI,CAACI,KAAK,CAAC,IAAI,CAACH,QAAQ,CAAC,EACzB;MACA,IAAI,CAACC,MAAM,GAAGF,IAAI;MAClB,IAAI,CAACG,WAAW,GAAG,KAAK;MACxB,IAAI,CAACE,eAAe,EAAE;MACtBC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACC,eAAe,IAAI,IAAI,CAACA,eAAe,CAACC,KAAK,EAAE;UACtD,IAAI,CAACD,eAAe,CAACC,KAAK,EAAE;;MAEhC,CAAC,EAAE,CAAC,CAAC;KACN,MAAM;MACLf,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B,IAAI,CAACQ,MAAM,GAAG,IAAI;MAClB,IAAI,CAACD,QAAQ,GAAGD,IAAI;MACpB,IAAI,CAACG,WAAW,GAAG,IAAI;;EAE3B;EAEAM,cAAcA,CAAA;IACZ,IAAI,CAACN,WAAW,GAAG,KAAK,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACI,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACG,IAAI,EAAE;;EAE/B;EAGAC,eAAeA,CAACC,KAAY;IAC1B,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;IAGxC,IAAI,CAACA,mBAAmB,GAAGP,UAAU,CAAC,MAAK;MACzC,IAAI,CAACS,kBAAkB,CAACH,KAAK,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC;EACR;EAEQG,kBAAkBA,CAACH,KAAY;IACrC;IACA,IACE,CAAC,IAAI,CAACL,eAAe,IACrB,CAAC,IAAI,CAACA,eAAe,CAACS,MAAM,IAC5B,CAAC,IAAI,CAACT,eAAe,CAACS,MAAM,EAAE,EAC9B;MACA;;IAGF,IAAI,IAAI,CAACb,WAAW,EAAE;MACpB;;IAGF,MAAMc,MAAM,GAAGL,KAAK,CAACK,MAAqB;IAE1C,IAAI,CAACA,MAAM,EAAE;IAEb,IACEA,MAAM,CAACC,OAAO,CAAC,MAAM,CAAC,IACtBD,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,SAAS,CAAC,IACpCH,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,EAC1C;MACA;;IAGF,IACEH,MAAM,CAACI,OAAO,KAAK,OAAO,IAC1BJ,MAAM,CAACK,YAAY,CAAC,MAAM,CAAC,KAAK,WAAW,EAC3C;MACA;;IAGF,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;IAClE,IAAIF,iBAAiB,IAAIA,iBAAiB,CAACH,QAAQ,CAACH,MAAM,CAAC,EAAE;MAC3D;;IAGF,IAAI,CAACV,eAAe,CAACC,KAAK,EAAE;EAC9B;EAEAkB,cAAcA,CAAA;IACZ,IAAI,CAACzB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACE,eAAe,EAAE;EACxB;EAEAsB,aAAaA,CAACC,YAA4B,EAAEC,KAAa;IACvD,MAAMC,MAAM,GAAG,IAAI,CAACC,SAAS,CAACC,KAAK,CAACH,KAAK,CAAC;IAC1C,OAAOC,MAAM,IAAI,IAAI,CAACG,QAAQ,CAACC,OAAO,CAACC,OAAO,CAACC,IAAI,CAACN,MAAM,CAAC,CAAC,GACxDK,OAAO,CAACC,IAAI,CAACN,MAAM,CAAC,GACpBF,YAAY;EAClB;EAEAS,SAASA,CAACrC,IAAa;IACrB,OACE,IAAI,CAACC,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZ,IAAI,CAACoC,WAAW,IAChBtC,IAAI,CAACI,KAAK,CAAC,IAAI,CAACH,QAAQ,CAAC,IACzBD,IAAI,CAACuC,MAAM,CAAC,IAAI,CAACD,WAAW,CAAC;EAEjC;EAEAE,QAAQA,CAACxC,IAAa;IACpB,OAAO,IAAI,CAACE,MAAM,IAAIF,IAAI,CAACI,KAAK,CAAC,IAAI,CAACH,QAAQ,CAAC,IAAID,IAAI,CAACuC,MAAM,CAAC,IAAI,CAACrC,MAAM,CAAC;EAC7E;EAEAuC,OAAOA,CAACzC,IAAa;IACnB,OACEA,IAAI,CAAC0C,MAAM,CAAC,IAAI,CAACzC,QAAQ,CAAC,IACzB,IAAI,CAACC,MAAM,IAAIF,IAAI,CAAC0C,MAAM,CAAC,IAAI,CAACxC,MAAM,CAAE,IACzC,IAAI,CAACsC,QAAQ,CAACxC,IAAI,CAAC,IACnB,IAAI,CAACqC,SAAS,CAACrC,IAAI,CAAC;EAExB;EAEA2C,iBAAiBA,CAAA;IACflD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACkD,SAAS,CAAC;IACjD,IAAI,CAACrD,qBAAqB,EAAE;EAC9B;EAEQA,qBAAqBA,CAAA;IAC3B;IACA,IAAI,IAAI,CAACsD,wBAAwB,EAAE;MACjC,IAAI,CAACA,wBAAwB,CAACC,gBAAgB,EAAE;;EAEpD;EAEAzC,eAAeA,CAAA;IACb,IAAI,IAAI,CAACJ,QAAQ,IAAI,IAAI,CAACC,MAAM,EAAE;MAChC,MAAM6C,UAAU,GAAGC,MAAM,CACvB,GAAG,IAAI,CAAC/C,QAAQ,CAACgD,IAAI,IAAI,IAAI,CAAChD,QAAQ,CAACiD,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAACnD,QAAQ,CAACoD,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,MAAME,QAAQ,GAAGN,MAAM,CACrB,GAAG,IAAI,CAAC9C,MAAM,CAAC+C,IAAI,IAAI,IAAI,CAAC/C,MAAM,CAACgD,KAAK,CACrCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAClD,MAAM,CAACmD,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACrE;MAED,IAAI,CAACR,SAAS,CAACW,UAAU,GAAGR,UAAU,CAACS,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAACZ,SAAS,CAACa,QAAQ,GAAGH,QAAQ,CAACE,MAAM,CAAC,YAAY,CAAC;MACvD,IAAI,CAACE,cAAc,GAAG,GAAGX,UAAU,CAACS,MAAM,CACxC,cAAc,CACf,MAAMF,QAAQ,CAACE,MAAM,CAAC,cAAc,CAAC,EAAE;MAExC,IAAI,CAACb,iBAAiB,EAAE;KACzB,MAAM,IAAI,IAAI,CAAC1C,QAAQ,EAAE;MACxB,MAAM8C,UAAU,GAAGC,MAAM,CACvB,GAAG,IAAI,CAAC/C,QAAQ,CAACgD,IAAI,IAAI,IAAI,CAAChD,QAAQ,CAACiD,KAAK,CACzCC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAACnD,QAAQ,CAACoD,GAAG,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,IAAI,CAACR,SAAS,CAACW,UAAU,GAAGR,UAAU,CAACS,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAACZ,SAAS,CAACa,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAGX,UAAU,CAACS,MAAM,CAAC,cAAc,CAAC;KACxD,MAAM;MACL,IAAI,CAACZ,SAAS,CAACW,UAAU,GAAG,IAAI;MAChC,IAAI,CAACX,SAAS,CAACa,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACf,iBAAiB,EAAE;;EAE5B;EAEA3E,eAAeA,CAAA;IACb,IAAI,IAAI,CAACF,QAAQ,EAAE;MACjB,IAAI,CAAC6F,KAAK,CACPC,GAAG,CACF,GAAGC,WAAW,CAACC,MAAM,YAAY,IAAI,CAAChG,QAAQ,+BAA+B,CAC9E,CACAH,SAAS,CACPC,IAAI,IAAI;QACP,IAAI,CAACmG,WAAW,GAAGnG,IAAI,CAACA,IAAI;MAC9B,CAAC,EACAK,KAAK,IAAI;QACRC,IAAI,CAACC,IAAI,CAAC;UACRC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAEJ,KAAK,CAACK,OAAO;UACnBC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAACC,iBAAiB,CAACC,OAAO,CAAC,IAAI;SACvD,CAAC;MACJ,CAAC,CACF;;EAEP;EAEAsF,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAAC/G,MAAM,CAACuB,OAAO,CAAC,gBAAgB,CAAC;MAClDyF,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,IAAI,CAACpH,MAAM,CAACuB,OAAO,CAAC,MAAM,CAAC;UACjC8F,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACpH,MAAM,CAACuB,OAAO,CAAC,aAAa,CAAC;UACxC8F,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACpH,MAAM,CAACuB,OAAO,CAAC,gBAAgB,CAAC;UAC3C8F,MAAM,EAAE;SACT;;KAGN;IAED,IAAI,CAAClH,iBAAiB,EAAE;IACxB,IAAI,CAACsB,SAAS,EAAE;EAClB;EAEA6F,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC5D,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;EAE1C;EAAC,QAAA6D,CAAA;qBAhTUzH,sBAAsB,EAAAF,EAAA,CAAA4H,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA9H,EAAA,CAAA4H,iBAAA,CAAAG,EAAA,CAAAC,KAAA;EAAA;EAAA,QAAAC,EAAA;UAAtB/H,sBAAsB;IAAAgI,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QDZnCxI,EAAA,CAAA0I,cAAA,aAA+C;QAG3C1I,EAAA,CAAAC,SAAA,4BAAyE;QAEzED,EAAA,CAAA0I,cAAA,iBAAkC;QAQpB1I,EAAA,CAAAC,SAAA,aAA2C;QAC3CD,EAAA,CAAA2I,MAAA,IACF;;QAAA3I,EAAA,CAAA4I,YAAA,EAAI;QACJ5I,EAAA,CAAA6I,UAAA,KAAAC,8CAAA,0BAEc;QAChB9I,EAAA,CAAA4I,YAAA,EAAK;QACL5I,EAAA,CAAA0I,cAAA,cAAkC;QAE9B1I,EAAA,CAAAC,SAAA,aAAyC;QACzCD,EAAA,CAAA2I,MAAA,IACF;;QAAA3I,EAAA,CAAA4I,YAAA,EAAI;QACJ5I,EAAA,CAAA6I,UAAA,KAAAE,8CAAA,0BAEc;QAChB/I,EAAA,CAAA4I,YAAA,EAAK;QAGT5I,EAAA,CAAAC,SAAA,eAA6C;QAC/CD,EAAA,CAAA4I,YAAA,EAAM;;;;QA7BU5I,EAAA,CAAAgJ,SAAA,GAA+B;QAA/BhJ,EAAA,CAAAiJ,UAAA,kBAAAR,GAAA,CAAAvB,aAAA,CAA+B;QAWrClH,EAAA,CAAAgJ,SAAA,IACF;QADEhJ,EAAA,CAAAkJ,kBAAA,MAAAlJ,EAAA,CAAAmJ,WAAA,2BACF;QAQEnJ,EAAA,CAAAgJ,SAAA,GACF;QADEhJ,EAAA,CAAAkJ,kBAAA,MAAAlJ,EAAA,CAAAmJ,WAAA,8BACF;QAODnJ,EAAA,CAAAgJ,SAAA,GAAoB;QAApBhJ,EAAA,CAAAiJ,UAAA,iBAAAG,GAAA,CAAoB;;;;;;;AC+FjCC,UAAA,EADCC,YAAY,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC,6DAS1C", "names": ["i0", "ɵɵelement", "LeagueReportsComponent", "constructor", "_trans", "_titleService", "setTitle", "_getCurrentSeason", "_loadingService", "show", "_registrationService", "getAllSeasonActive", "subscribe", "data", "seasons", "seasonId", "id", "_getTournaments", "error", "<PERSON><PERSON>", "fire", "title", "text", "message", "icon", "confirmButtonText", "_translateService", "instant", "dismiss", "_getClubs", "_clubService", "getAllClubs", "res", "clubs", "onSelectSeason", "$event", "Promise", "resolve", "reject", "tournamentId", "refreshChildDataTable", "onSelectClub", "console", "log", "clubId", "onSelectTournament", "onSelectMatchStatus", "matchStatus", "onDateSelection", "date", "fromDate", "toDate", "isSelecting", "after", "updateDateRange", "setTimeout", "dateRangePicker", "close", "openDatePicker", "open", "onDocumentClick", "event", "clickOutsideTimeout", "clearTimeout", "handleClickOutside", "isOpen", "target", "closest", "classList", "contains", "tagName", "getAttribute", "datepickerElement", "document", "querySelector", "clearDateRange", "validateInput", "currentValue", "input", "parsed", "formatter", "parse", "calendar", "<PERSON><PERSON><PERSON><PERSON>", "NgbDate", "from", "isHovered", "hoveredDate", "before", "isInside", "isRange", "equals", "onDateRangeChange", "date<PERSON><PERSON><PERSON>", "leagueTableViewComponent", "refreshDataTable", "fromMoment", "moment", "year", "month", "toString", "padStart", "day", "toMoment", "start_date", "format", "end_date", "dateRangeValue", "_http", "get", "environment", "apiUrl", "tournaments", "ngOnInit", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "type", "links", "name", "isLink", "ngOnDestroy", "_", "ɵɵdirectiveInject", "i1", "TranslateService", "i2", "Title", "_2", "selectors", "decls", "vars", "consts", "template", "LeagueReportsComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "LeagueReportsComponent_ng_template_14_Template", "LeagueReportsComponent_ng_template_20_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "_r0", "__decorate", "HostListener"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.html", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.ts"], "sourcesContent": ["<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n    <section id=\"league-reports-page\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <!-- Tabs Section -->\r\n          <div class=\"card\">\r\n            <ul ngbNav #nav=\"ngbNav\" class=\"nav-tabs m-0\">\r\n              <li ngbNavItem=\"league_table\">\r\n                <a ngbNavLink>\r\n                  <i class=\"fa-light fa-table-list mr-1\"></i>\r\n                  {{ 'Table View' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <app-league-table-view></app-league-table-view>\r\n                </ng-template>\r\n              </li>\r\n              <li ngbNavItem=\"schedule_matches\">\r\n                <a ngbNavLink>\r\n                  <i class=\"fa-light fa-calendar mr-1\"></i>\r\n                  {{ 'Schedule View' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <app-schedule-view></app-schedule-view>\r\n                </ng-template>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div [ngbNavOutlet]=\"nav\" class=\"mt-2\"></div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</div>\r\n\r\n", "import {\r\n  Component,\r\n  OnInit\r\n} from '@angular/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { Title } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'app-league-reports',\r\n  templateUrl: './league-reports.component.html',\r\n  styleUrls: ['./league-reports.component.scss'],\r\n})\r\nexport class LeagueReportsComponent implements OnInit\r\n{\r\n  public contentHeader: object;\r\n\r\n  constructor(\r\n    public _trans: TranslateService,\r\n    public _titleService: Title\r\n  ) {\r\n    this._titleService.setTitle('Matches Report');\r\n  }\r\n\r\n  _getCurrentSeason() {\r\n    this._loadingService.show();\r\n    this._registrationService.getAllSeasonActive().subscribe(\r\n      (data) => {\r\n        this.seasons = data;\r\n        this.seasonId = this.seasons[0].id;\r\n        this._getTournaments(); // Fetch tournaments after getting seasons\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      },\r\n      () => {\r\n        this._loadingService.dismiss();\r\n      }\r\n    );\r\n  }\r\n\r\n  _getClubs() {\r\n    this._clubService.getAllClubs().subscribe(\r\n      (res) => {\r\n        this.clubs = res.data;\r\n      },\r\n      (error) => {\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: error.message,\r\n          icon: 'error',\r\n          confirmButtonText: this._translateService.instant('OK')\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  onSelectSeason($event: any) {\r\n    return new Promise((resolve, reject) => {\r\n      this.seasonId = $event;\r\n      this._getTournaments(); // Fetch tournaments when season changes\r\n      // Reset tournament selection when season changes\r\n      this.tournamentId = null;\r\n      this.refreshChildDataTable();\r\n      resolve(true);\r\n    });\r\n  }\r\n\r\n  onSelectClub($event: any) {\r\n    console.log(`onSelectClub: ${$event}`);\r\n    this.clubId = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onSelectTournament($event: any) {\r\n    console.log(`onSelectTournament: ${$event}`);\r\n    this.tournamentId = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onSelectMatchStatus($event: any) {\r\n    console.log(`onSelectMatchStatus: ${$event}`);\r\n    this.matchStatus = $event;\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  onDateSelection(date: NgbDate) {\r\n\r\n    if (!this.fromDate && !this.toDate) {\r\n      console.log('Setting From Date');\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    } else if (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      date &&\r\n      date.after(this.fromDate)\r\n    ) {\r\n      this.toDate = date;\r\n      this.isSelecting = false;\r\n      this.updateDateRange();\r\n      setTimeout(() => {\r\n        if (this.dateRangePicker && this.dateRangePicker.close) {\r\n          this.dateRangePicker.close();\r\n        }\r\n      }, 0);\r\n    } else {\r\n      console.log('reset date form');\r\n      this.toDate = null;\r\n      this.fromDate = date;\r\n      this.isSelecting = true;\r\n    }\r\n  }\r\n\r\n  openDatePicker() {\r\n    this.isSelecting = false; // Reset selection state when opening\r\n    if (this.dateRangePicker) {\r\n      this.dateRangePicker.open();\r\n    }\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event) {\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n\r\n    this.clickOutsideTimeout = setTimeout(() => {\r\n      this.handleClickOutside(event);\r\n    }, 50);\r\n  }\r\n\r\n  private handleClickOutside(event: Event) {\r\n    // Check if datepicker is open\r\n    if (\r\n      !this.dateRangePicker ||\r\n      !this.dateRangePicker.isOpen ||\r\n      !this.dateRangePicker.isOpen()\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (this.isSelecting) {\r\n      return;\r\n    }\r\n\r\n    const target = event.target as HTMLElement;\r\n\r\n    if (!target) return;\r\n\r\n    if (\r\n      target.closest('.btn') ||\r\n      target.classList.contains('feather') ||\r\n      target.classList.contains('icon-calendar')\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (\r\n      target.tagName === 'INPUT' &&\r\n      target.getAttribute('name') === 'daterange'\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    const datepickerElement = document.querySelector('ngb-datepicker');\r\n    if (datepickerElement && datepickerElement.contains(target)) {\r\n      return;\r\n    }\r\n\r\n    this.dateRangePicker.close();\r\n  }\r\n\r\n  clearDateRange() {\r\n    this.fromDate = null;\r\n    this.toDate = null;\r\n    this.isSelecting = false;\r\n    this.updateDateRange();\r\n  }\r\n\r\n  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {\r\n    const parsed = this.formatter.parse(input);\r\n    return parsed && this.calendar.isValid(NgbDate.from(parsed))\r\n      ? NgbDate.from(parsed)\r\n      : currentValue;\r\n  }\r\n\r\n  isHovered(date: NgbDate) {\r\n    return (\r\n      this.fromDate &&\r\n      !this.toDate &&\r\n      this.hoveredDate &&\r\n      date.after(this.fromDate) &&\r\n      date.before(this.hoveredDate)\r\n    );\r\n  }\r\n\r\n  isInside(date: NgbDate) {\r\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\r\n  }\r\n\r\n  isRange(date: NgbDate) {\r\n    return (\r\n      date.equals(this.fromDate) ||\r\n      (this.toDate && date.equals(this.toDate)) ||\r\n      this.isInside(date) ||\r\n      this.isHovered(date)\r\n    );\r\n  }\r\n\r\n  onDateRangeChange() {\r\n    console.log('onDateRangeChange:', this.dateRange);\r\n    this.refreshChildDataTable();\r\n  }\r\n\r\n  private refreshChildDataTable() {\r\n    // Refresh the league table view component's DataTable\r\n    if (this.leagueTableViewComponent) {\r\n      this.leagueTableViewComponent.refreshDataTable();\r\n    }\r\n  }\r\n\r\n  updateDateRange() {\r\n    if (this.fromDate && this.toDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      const toMoment = moment(\r\n        `${this.toDate.year}-${this.toDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`\r\n      );\r\n\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\r\n      this.dateRangeValue = `${fromMoment.format(\r\n        'MMM DD, YYYY'\r\n      )} - ${toMoment.format('MMM DD, YYYY')}`;\r\n\r\n      this.onDateRangeChange();\r\n    } else if (this.fromDate) {\r\n      const fromMoment = moment(\r\n        `${this.fromDate.year}-${this.fromDate.month\r\n          .toString()\r\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\r\n      );\r\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\r\n    } else {\r\n      this.dateRange.start_date = null;\r\n      this.dateRange.end_date = null;\r\n      this.dateRangeValue = '';\r\n      this.onDateRangeChange();\r\n    }\r\n  }\r\n\r\n  _getTournaments() {\r\n    if (this.seasonId) {\r\n      this._http\r\n        .get<any>(\r\n          `${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`\r\n        )\r\n        .subscribe(\r\n          (data) => {\r\n            this.tournaments = data.data;\r\n          },\r\n          (error) => {\r\n            Swal.fire({\r\n              title: 'Error',\r\n              text: error.message,\r\n              icon: 'error',\r\n              confirmButtonText: this._translateService.instant('OK'),\r\n            });\r\n          }\r\n        );\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Matches Report'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Home'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Tournaments'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Matches Report'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    this._getCurrentSeason();\r\n    this._getClubs();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.clickOutsideTimeout) {\r\n      clearTimeout(this.clickOutsideTimeout);\r\n    }\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}