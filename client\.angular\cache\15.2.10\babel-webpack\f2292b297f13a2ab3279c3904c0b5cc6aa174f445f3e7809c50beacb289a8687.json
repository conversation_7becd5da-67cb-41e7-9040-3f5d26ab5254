{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactivevn/ezleague-core/client/node_modules/@angular-devkit/build-angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DataTableDirective } from 'angular-datatables';\nimport { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport { NgbDate } from '@ng-bootstrap/ng-bootstrap';\nimport moment from 'moment';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"app/services/commons.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@core/components/core-sidebar/core-sidebar.service\";\nimport * as i5 from \"app/services/export.service\";\nimport * as i6 from \"app/services/loading.service\";\nimport * as i7 from \"app/services/registration.service\";\nimport * as i8 from \"app/services/club.service\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/forms\";\nimport * as i12 from \"@core/components/core-sidebar/core-sidebar.component\";\nimport * as i13 from \"angular-datatables\";\nimport * as i14 from \"@ng-select/ng-select\";\nimport * as i15 from \"../../../../components/btn-dropdown-action/btn-dropdown-action.component\";\nimport * as i16 from \"../../../../components/editor-sidebar/editor-sidebar.component\";\nconst _c0 = [\"rowActionBtn\"];\nconst _c1 = [\"dateRangePicker\"];\nfunction LeagueTableViewComponent_ng_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const season_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", season_r11.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, season_r11.name), \" \");\n  }\n}\nfunction LeagueTableViewComponent_ng_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tournament_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", tournament_r12.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, tournament_r12.name), \" \");\n  }\n}\nfunction LeagueTableViewComponent_ng_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const club_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", club_r13.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, club_r13.code), \" \");\n  }\n}\nfunction LeagueTableViewComponent_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵlistener(\"mouseenter\", function LeagueTableViewComponent_ng_template_35_Template_span_mouseenter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const date_r14 = restoredCtx.date;\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.hoveredDate = date_r14);\n    })(\"mouseleave\", function LeagueTableViewComponent_ng_template_35_Template_span_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.hoveredDate = null);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r14 = ctx.date;\n    const focused_r15 = ctx.focused;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"focused\", focused_r15)(\"range\", ctx_r5.isRange(date_r14))(\"faded\", ctx_r5.isHovered(date_r14) || ctx_r5.isInside(date_r14));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", date_r14.day, \" \");\n  }\n}\nfunction LeagueTableViewComponent_ng_template_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelement(0, \"hr\", 25);\n    i0.ɵɵelementStart(1, \"div\", 26)(2, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function LeagueTableViewComponent_ng_template_37_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      const _r3 = i0.ɵɵreference(30);\n      ctx_r19.clearDateRange();\n      return i0.ɵɵresetView(_r3.close());\n    });\n    i0.ɵɵtext(3, \" Clear \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function LeagueTableViewComponent_ng_template_37_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r20);\n      i0.ɵɵnextContext();\n      const _r3 = i0.ɵɵreference(30);\n      return i0.ɵɵresetView(_r3.close());\n    });\n    i0.ɵɵtext(5, \" Close \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LeagueTableViewComponent_ng_option_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r22.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, status_r22.label), \" \");\n  }\n}\nfunction LeagueTableViewComponent_ng_template_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-btn-dropdown-action\", 29);\n    i0.ɵɵlistener(\"emitter\", function LeagueTableViewComponent_ng_template_51_Template_app_btn_dropdown_action_emitter_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const emitter_r24 = restoredCtx.captureEvents;\n      return i0.ɵɵresetView(emitter_r24($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r23 = ctx.adtData;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"actions\", ctx_r10.rowActions)(\"data\", data_r23);\n  }\n}\nexport class LeagueTableViewComponent {\n  constructor(_http, _commonsService, _translateService, _coreSidebarService, _exportService, _loadingService, _registrationService, _clubService, calendar, formatter) {\n    this._http = _http;\n    this._commonsService = _commonsService;\n    this._translateService = _translateService;\n    this._coreSidebarService = _coreSidebarService;\n    this._exportService = _exportService;\n    this._loadingService = _loadingService;\n    this._registrationService = _registrationService;\n    this._clubService = _clubService;\n    this.calendar = calendar;\n    this.formatter = formatter;\n    this.dtElement = DataTableDirective;\n    this.dtTrigger = new Subject();\n    this.dtOptions = {};\n    this.clubId = null;\n    this.tournamentId = null;\n    this.dateRange = {\n      start_date: null,\n      end_date: null\n    };\n    this.dateRangeValue = '';\n    this.hoveredDate = null;\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false;\n    this.matchStatus = 'all';\n    this.isTableLoading = false;\n    this.table_name = 'team-table';\n    this.matchStatusOptions = [{\n      label: 'All Status',\n      value: 'all'\n    }, {\n      label: 'Upcoming',\n      value: 'upcoming'\n    }, {\n      label: 'Active',\n      value: 'active'\n    }, {\n      label: 'Completed',\n      value: 'completed'\n    }, {\n      label: 'Cancelled',\n      value: 'cancelled'\n    }];\n    this.params = {\n      editor_id: this.table_name,\n      title: {\n        create: this._translateService.instant('Create New Tournament'),\n        edit: 'Edit team',\n        remove: 'Delete team'\n      },\n      url: `${environment.apiUrl}/teams/editor`,\n      method: 'POST',\n      action: 'create'\n    };\n    this.fields = [{\n      key: 'home_score',\n      type: 'number',\n      props: {\n        label: this._translateService.instant('Home team score'),\n        placeholder: this._translateService.instant('Enter score of team'),\n        required: true\n      }\n    }, {\n      key: 'away_score',\n      type: 'number',\n      props: {\n        label: this._translateService.instant('Away team score'),\n        placeholder: this._translateService.instant('Enter score of team'),\n        required: true\n      }\n    }];\n  }\n  ngOnInit() {\n    this._getCurrentSeason();\n    this._getClubs();\n    this.buildDataTable();\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.dtTrigger.next(this.dtOptions);\n    }, 500);\n  }\n  buildDataTable() {\n    var _this = this;\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      rowId: 'id',\n      processing: true,\n      language: {\n        ...this._commonsService.dataTableDefaults.lang,\n        processing: ``\n      },\n      ajax: (dataTablesParameters, callback) => {\n        // Build query parameters for all filters\n        let params = new URLSearchParams();\n        if (this.clubId != undefined && this.clubId !== null) {\n          params.append('club_id', this.clubId);\n        }\n        if (this.tournamentId != undefined && this.tournamentId !== null) {\n          params.append('tournament_id', this.tournamentId);\n        }\n        if (this.dateRange?.start_date) {\n          params.append('start_date', this.dateRange.start_date);\n        }\n        if (this.dateRange?.end_date) {\n          params.append('end_date', this.dateRange.end_date);\n        }\n        if (this.matchStatus && this.matchStatus !== 'all') {\n          params.append('match_status', this.matchStatus);\n        }\n        const queryString = params.toString();\n        const url = `${environment.apiUrl}/seasons/${this.seasonId}/matches${queryString ? '?' + queryString : ''}`;\n        this.isTableLoading = true;\n        this._http.post(url, dataTablesParameters).subscribe(resp => {\n          this.isTableLoading = false;\n          callback({\n            recordsTotal: resp.recordsTotal,\n            recordsFiltered: resp.recordsFiltered,\n            data: resp.data\n          });\n        }, error => {\n          this.isTableLoading = false;\n          console.error('Error loading table data:', error);\n          callback({\n            recordsTotal: 0,\n            recordsFiltered: 0,\n            data: []\n          });\n        });\n      },\n      responsive: false,\n      scrollX: true,\n      columnDefs: [{\n        responsivePriority: 1,\n        targets: -1\n      }],\n      columns: [{\n        title: this._translateService.instant('No.'),\n        data: null,\n        className: 'font-weight-bolder',\n        type: 'any-number',\n        render: function (data, type, row, metadata) {\n          return metadata.row + 1;\n        }\n      }, {\n        title: this._translateService.instant('Tournament'),\n        data: 'name',\n        className: 'font-weight-bolder',\n        type: 'any-number',\n        render: {\n          display: (data, type, row) => {\n            return data ?? 'TBD';\n          },\n          filter: (data, type, row) => {\n            return data;\n          }\n        }\n      }, {\n        title: this._translateService.instant('Round'),\n        data: 'round_name',\n        render: function (data) {\n          const displayData = data || 'TBD';\n          return `<div class=\"text-center\" style=\"width:max-content;\">${displayData}</div>`;\n        }\n      }, {\n        title: this._translateService.instant('Date'),\n        data: 'start_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          const displayDate = !data || data === 'TBD' ? 'TBD' : moment(data).format('YYYY-MM-DD');\n          return `<div class=\"text-center\" style=\"min-width: max-content;\">${displayDate}</div>`;\n        }\n      }, {\n        title: this._translateService.instant('Start time'),\n        data: 'start_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          return moment(data).format('HH:mm');\n        }\n      }, {\n        title: this._translateService.instant('End time'),\n        data: 'end_time',\n        className: 'text-center',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          return moment(data).format('HH:mm');\n        }\n      }, {\n        title: this._translateService.instant('Location'),\n        data: 'location',\n        render: function (data, type, row) {\n          if (!data || data == 'TBD') {\n            return 'TBD';\n          }\n          return data;\n        }\n      }, {\n        title: this._translateService.instant('Home Team'),\n        data: 'home_team_name',\n        className: 'text-center'\n      }, {\n        title: 'VS',\n        data: null,\n        className: 'text-center',\n        render: function (data, type, row) {\n          return 'vs';\n        }\n      }, {\n        title: this._translateService.instant('Away Team'),\n        data: 'away_team_name',\n        className: 'text-center'\n      }, {\n        title: this._translateService.instant('Home score'),\n        data: 'home_score',\n        className: 'text-center'\n      }, {\n        data: null,\n        className: 'text-center',\n        render: function (data, type, row) {\n          return '-';\n        }\n      }, {\n        title: this._translateService.instant('Away score'),\n        data: 'away_score',\n        className: 'text-center'\n      }],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [{\n          text: `<i class=\"fa-solid fa-file-csv mr-1\"></i> ${this._translateService.instant('Export CSV')}`,\n          extend: 'csv',\n          action: function () {\n            var _ref = _asyncToGenerator(function* (e, dt, button, config) {\n              const data = dt.buttons.exportData();\n              // Generate filename with current filter information\n              let filename = 'Matches_Report';\n              if (_this.seasonId) {\n                const season = _this.seasons?.find(s => s.id == _this.seasonId);\n                if (season) {\n                  filename += `_${season.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.tournamentId) {\n                const tournament = _this.tournaments?.find(t => t.id == _this.tournamentId);\n                if (tournament) {\n                  filename += `_${tournament.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.clubId) {\n                const club = _this.clubs?.find(c => c.id == _this.clubId);\n                if (club) {\n                  filename += `_${club.code.replace(/\\s+/g, '_')}`;\n                }\n              }\n              if (_this.matchStatus && _this.matchStatus !== 'all') {\n                filename += `_${_this.matchStatus}`;\n              }\n              if (_this.dateRange?.start_date && _this.dateRange?.end_date) {\n                filename += `_${_this.dateRange.start_date}_to_${_this.dateRange.end_date}`;\n              } else if (_this.dateRange?.start_date) {\n                filename += `_from_${_this.dateRange.start_date}`;\n              }\n              filename += '.csv';\n              yield _this._exportService.exportCsv(data, filename);\n            });\n            return function action(_x, _x2, _x3, _x4) {\n              return _ref.apply(this, arguments);\n            };\n          }()\n        }]\n      }\n    };\n  }\n  refreshDataTable() {\n    if (this.dtElement?.dtInstance) {\n      this.dtElement.dtInstance.then(dtInstance => {\n        dtInstance.ajax.reload();\n      });\n    }\n  }\n  // Filter methods\n  _getCurrentSeason() {\n    this._loadingService.show();\n    this._registrationService.getAllSeasonActive().subscribe(data => {\n      this.seasons = data;\n      this.seasonId = this.seasons[0].id;\n      this._getTournaments();\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    }, () => {\n      this._loadingService.dismiss();\n    });\n  }\n  _getClubs() {\n    this._clubService.getAllClubs().subscribe(res => {\n      this.clubs = res.data;\n    }, error => {\n      Swal.fire({\n        title: 'Error',\n        text: error.message,\n        icon: 'error',\n        confirmButtonText: this._translateService.instant('OK')\n      });\n    });\n  }\n  _getTournaments() {\n    if (this.seasonId) {\n      this._http.get(`${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`).subscribe(data => {\n        this.tournaments = data.data;\n      }, error => {\n        Swal.fire({\n          title: 'Error',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      });\n    }\n  }\n  onSelectSeason($event) {\n    return new Promise((resolve, reject) => {\n      this.seasonId = $event;\n      this._getTournaments();\n      this.tournamentId = null;\n      this.refreshDataTable();\n      resolve(true);\n    });\n  }\n  onSelectClub($event) {\n    this.clubId = $event;\n    this.refreshDataTable();\n  }\n  onSelectTournament($event) {\n    this.tournamentId = $event;\n    this.refreshDataTable();\n  }\n  onSelectMatchStatus($event) {\n    this.matchStatus = $event;\n    this.refreshDataTable();\n  }\n  // Date picker methods\n  onDateSelection(date) {\n    if (!this.fromDate && !this.toDate) {\n      this.fromDate = date;\n      this.isSelecting = true;\n    } else if (this.fromDate && !this.toDate && date && date.after(this.fromDate)) {\n      this.toDate = date;\n      this.isSelecting = false;\n      this.updateDateRange();\n      setTimeout(() => {\n        if (this.dateRangePicker && this.dateRangePicker.close) {\n          this.dateRangePicker.close();\n        }\n      }, 0);\n    } else {\n      this.toDate = null;\n      this.fromDate = date;\n      this.isSelecting = true;\n    }\n  }\n  openDatePicker() {\n    this.isSelecting = false;\n    if (this.dateRangePicker) {\n      this.dateRangePicker.open();\n    }\n  }\n  onDocumentClick(event) {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n    this.clickOutsideTimeout = setTimeout(() => {\n      this.handleClickOutside(event);\n    }, 50);\n  }\n  handleClickOutside(event) {\n    if (!this.dateRangePicker || !this.dateRangePicker.isOpen || !this.dateRangePicker.isOpen()) {\n      return;\n    }\n    if (this.isSelecting) {\n      return;\n    }\n    const target = event.target;\n    if (!target) return;\n    if (target.closest('.btn') || target.classList.contains('feather') || target.classList.contains('icon-calendar')) {\n      return;\n    }\n    if (target.tagName === 'INPUT' && target.getAttribute('name') === 'daterange') {\n      return;\n    }\n    const datepickerElement = document.querySelector('ngb-datepicker');\n    if (datepickerElement && datepickerElement.contains(target)) {\n      return;\n    }\n    this.dateRangePicker.close();\n  }\n  clearDateRange() {\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false;\n    this.updateDateRange();\n  }\n  validateInput(currentValue, input) {\n    const parsed = this.formatter.parse(input);\n    return parsed && this.calendar.isValid(NgbDate.from(parsed)) ? NgbDate.from(parsed) : currentValue;\n  }\n  isHovered(date) {\n    return this.fromDate && !this.toDate && this.hoveredDate && date.after(this.fromDate) && date.before(this.hoveredDate);\n  }\n  isInside(date) {\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\n  }\n  isRange(date) {\n    return date.equals(this.fromDate) || this.toDate && date.equals(this.toDate) || this.isInside(date) || this.isHovered(date);\n  }\n  onDateRangeChange() {\n    this.refreshDataTable();\n  }\n  updateDateRange() {\n    if (this.fromDate && this.toDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      const toMoment = moment(`${this.toDate.year}-${this.toDate.month.toString().padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\n      this.dateRangeValue = `${fromMoment.format('MMM DD, YYYY')} - ${toMoment.format('MMM DD, YYYY')}`;\n      this.onDateRangeChange();\n    } else if (this.fromDate) {\n      const fromMoment = moment(`${this.fromDate.year}-${this.fromDate.month.toString().padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`);\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = null;\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\n    } else {\n      this.dateRange.start_date = null;\n      this.dateRange.end_date = null;\n      this.dateRangeValue = '';\n      this.onDateRangeChange();\n    }\n  }\n  ngOnDestroy() {\n    this.dtTrigger.unsubscribe();\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n  }\n  static #_ = this.ɵfac = function LeagueTableViewComponent_Factory(t) {\n    return new (t || LeagueTableViewComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.CommonsService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.CoreSidebarService), i0.ɵɵdirectiveInject(i5.ExportService), i0.ɵɵdirectiveInject(i6.LoadingService), i0.ɵɵdirectiveInject(i7.RegistrationService), i0.ɵɵdirectiveInject(i8.ClubService), i0.ɵɵdirectiveInject(i9.NgbCalendar), i0.ɵɵdirectiveInject(i9.NgbDateParserFormatter));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LeagueTableViewComponent,\n    selectors: [[\"app-league-table-view\"]],\n    viewQuery: function LeagueTableViewComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(DataTableDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rowActionBtn = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dateRangePicker = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dtElement = _t.first);\n      }\n    },\n    hostBindings: function LeagueTableViewComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function LeagueTableViewComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 53,\n    vars: 58,\n    consts: [[1, \"p-1\"], [1, \"row\", \"mb-1\"], [1, \"col-12\"], [\"for\", \"season\", 1, \"form-label\"], [3, \"searchable\", \"clearable\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-3\"], [\"for\", \"tournament\", 1, \"form-label\"], [\"for\", \"club\", 1, \"form-label\"], [\"for\", \"dateRange\", 1, \"form-label\"], [1, \"input-group\"], [\"name\", \"daterange\", \"ngbDatepicker\", \"\", \"readonly\", \"\", \"outsideDays\", \"hidden\", 1, \"form-control\", 3, \"placeholder\", \"value\", \"dayTemplate\", \"footerTemplate\", \"firstDayOfWeek\", \"displayMonths\", \"autoClose\", \"click\", \"dateSelect\"], [\"dateRangePicker\", \"ngbDatepicker\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"feather\", \"icon-calendar\"], [\"dayTemplate\", \"\"], [\"footerTemplate\", \"\"], [\"for\", \"matchStatus\", 1, \"form-label\"], [\"datatable\", \"\", 1, \"table\", \"border\", \"row-border\", \"hover\", 3, \"dtOptions\", \"dtTrigger\"], [\"overlayClass\", \"modal-backdrop\", 1, \"modal\", \"modal-slide-in\", \"sidebar-todo-modal\", \"fade\", 3, \"name\"], [3, \"table\", \"fields\", \"params\"], [\"rowActionBtn\", \"\"], [3, \"value\"], [1, \"custom-day\", 3, \"mouseenter\", \"mouseleave\"], [1, \"my-0\"], [1, \"d-flex\", \"justify-content-between\", \"p-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [\"btnStyle\", \"font-size:15px;color:black!important\", 3, \"actions\", \"data\", \"emitter\"]],\n    template: function LeagueTableViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"label\", 3);\n        i0.ɵɵtext(4);\n        i0.ɵɵpipe(5, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"ng-select\", 4);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueTableViewComponent_Template_ng_select_ngModelChange_6_listener($event) {\n          return ctx.seasonId = $event;\n        })(\"change\", function LeagueTableViewComponent_Template_ng_select_change_6_listener($event) {\n          return ctx.onSelectSeason($event);\n        });\n        i0.ɵɵpipe(7, \"translate\");\n        i0.ɵɵtemplate(8, LeagueTableViewComponent_ng_option_8_Template, 3, 4, \"ng-option\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"div\", 1)(10, \"div\", 6)(11, \"label\", 7);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"ng-select\", 4);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueTableViewComponent_Template_ng_select_ngModelChange_14_listener($event) {\n          return ctx.tournamentId = $event;\n        })(\"change\", function LeagueTableViewComponent_Template_ng_select_change_14_listener($event) {\n          return ctx.onSelectTournament($event);\n        });\n        i0.ɵɵpipe(15, \"translate\");\n        i0.ɵɵtemplate(16, LeagueTableViewComponent_ng_option_16_Template, 3, 4, \"ng-option\", 5);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 6)(18, \"label\", 8);\n        i0.ɵɵtext(19);\n        i0.ɵɵpipe(20, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"ng-select\", 4);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueTableViewComponent_Template_ng_select_ngModelChange_21_listener($event) {\n          return ctx.clubId = $event;\n        })(\"change\", function LeagueTableViewComponent_Template_ng_select_change_21_listener($event) {\n          return ctx.onSelectClub($event);\n        });\n        i0.ɵɵpipe(22, \"translate\");\n        i0.ɵɵtemplate(23, LeagueTableViewComponent_ng_option_23_Template, 3, 4, \"ng-option\", 5);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(24, \"div\", 6)(25, \"label\", 9);\n        i0.ɵɵtext(26);\n        i0.ɵɵpipe(27, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"div\", 10)(29, \"input\", 11, 12);\n        i0.ɵɵlistener(\"click\", function LeagueTableViewComponent_Template_input_click_29_listener() {\n          return ctx.openDatePicker();\n        })(\"dateSelect\", function LeagueTableViewComponent_Template_input_dateSelect_29_listener($event) {\n          return ctx.onDateSelection($event);\n        });\n        i0.ɵɵpipe(31, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"div\", 13)(33, \"button\", 14);\n        i0.ɵɵlistener(\"click\", function LeagueTableViewComponent_Template_button_click_33_listener() {\n          return ctx.openDatePicker();\n        });\n        i0.ɵɵelement(34, \"i\", 15);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(35, LeagueTableViewComponent_ng_template_35_Template, 2, 7, \"ng-template\", null, 16, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(37, LeagueTableViewComponent_ng_template_37_Template, 6, 0, \"ng-template\", null, 17, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"div\", 6)(40, \"label\", 18);\n        i0.ɵɵtext(41);\n        i0.ɵɵpipe(42, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"ng-select\", 4);\n        i0.ɵɵlistener(\"ngModelChange\", function LeagueTableViewComponent_Template_ng_select_ngModelChange_43_listener($event) {\n          return ctx.matchStatus = $event;\n        })(\"change\", function LeagueTableViewComponent_Template_ng_select_change_43_listener($event) {\n          return ctx.onSelectMatchStatus($event);\n        });\n        i0.ɵɵpipe(44, \"translate\");\n        i0.ɵɵtemplate(45, LeagueTableViewComponent_ng_option_45_Template, 3, 4, \"ng-option\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(46, \"div\", 1)(47, \"div\", 2);\n        i0.ɵɵelement(48, \"table\", 19);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(49, \"core-sidebar\", 20);\n        i0.ɵɵelement(50, \"app-editor-sidebar\", 21);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(51, LeagueTableViewComponent_ng_template_51_Template, 1, 2, \"ng-template\", null, 22, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const _r4 = i0.ɵɵreference(36);\n        const _r6 = i0.ɵɵreference(38);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 38, \"Season\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(7, 40, \"Select Season\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", false)(\"ngModel\", ctx.seasonId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.seasons);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 42, \"Tournament\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(15, 44, \"Select Tournament\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", true)(\"ngModel\", ctx.tournamentId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.tournaments);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 46, \"Club\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(22, 48, \"Select Club\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", true)(\"ngModel\", ctx.clubId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.clubs);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(27, 50, \"Date Range\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(31, 52, \"Select Date Range\"));\n        i0.ɵɵproperty(\"value\", ctx.formatter.format(ctx.fromDate) + (ctx.toDate ? \" - \" + ctx.formatter.format(ctx.toDate) : \"\"))(\"dayTemplate\", _r4)(\"footerTemplate\", _r6)(\"firstDayOfWeek\", 1)(\"displayMonths\", 2)(\"autoClose\", false);\n        i0.ɵɵadvance(12);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(42, 54, \"Match Status\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(44, 56, \"Select Status\"));\n        i0.ɵɵproperty(\"searchable\", false)(\"clearable\", false)(\"ngModel\", ctx.matchStatus);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.matchStatusOptions);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"dtOptions\", ctx.dtOptions)(\"dtTrigger\", ctx.dtTrigger);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"name\", ctx.table_name);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"table\", ctx.dtElement)(\"fields\", ctx.fields)(\"params\", ctx.params);\n      }\n    },\n    dependencies: [i10.NgForOf, i11.NgControlStatus, i11.NgModel, i9.NgbInputDatepicker, i12.CoreSidebarComponent, i13.DataTableDirective, i14.NgSelectComponent, i14.ɵr, i15.BtnDropdownActionComponent, i16.EditorSidebarComponent, i3.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": ";AAWA,SAASA,kBAAkB,QAAQ,oBAAoB;AAMvD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,OAAO,QAAQ,MAAM;AAI9B,SAAsBC,OAAO,QAAgC,4BAA4B;AACzF,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;ICbtBC,EAAA,CAAAC,cAAA,oBAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAC,UAAA,CAAAC,EAAA,CAAmB;IAC3DN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAJ,UAAA,CAAAK,IAAA,OACF;;;;;IAaAV,EAAA,CAAAC,cAAA,oBAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAO,cAAA,CAAAL,EAAA,CAAuB;IACvEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAE,cAAA,CAAAD,IAAA,OACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF0BH,EAAA,CAAAI,UAAA,UAAAQ,QAAA,CAAAN,EAAA,CAAiB;IACrDN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAG,QAAA,CAAAC,IAAA,OACF;;;;;;IAkCAb,EAAA,CAAAC,cAAA,eAOC;IAFCD,EAAA,CAAAc,UAAA,wBAAAC,4EAAA;MAAA,MAAAC,WAAA,GAAAhB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,IAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAAF,OAAA,CAAAG,WAAA,GAAAL,QAAA;IAAA,EAAiC,wBAAAM,4EAAA;MAAAzB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAQ,OAAA,GAAA1B,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAAG,OAAA,CAAAF,WAAA,GACL,IAAI;IAAA,EADC;IAGjCxB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAPLH,EAAA,CAAA2B,WAAA,YAAAC,WAAA,CAAyB,UAAAC,MAAA,CAAAC,OAAA,CAAAX,QAAA,YAAAU,MAAA,CAAAE,SAAA,CAAAZ,QAAA,KAAAU,MAAA,CAAAG,QAAA,CAAAb,QAAA;IAMzBnB,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAW,QAAA,CAAAc,GAAA,MACF;;;;;;IAIAjC,EAAA,CAAAkC,SAAA,aAAiB;IACjBlC,EAAA,CAAAC,cAAA,cAAgD;IAI5CD,EAAA,CAAAc,UAAA,mBAAAqB,yEAAA;MAAAnC,EAAA,CAAAiB,aAAA,CAAAmB,IAAA;MAAA,MAAAC,OAAA,GAAArC,EAAA,CAAAsB,aAAA;MAAA,MAAAgB,GAAA,GAAAtC,EAAA,CAAAuC,WAAA;MAASF,OAAA,CAAAG,cAAA,EAAgB;MAAA,OAAExC,EAAA,CAAAuB,WAAA,CAAAe,GAAA,CAAAG,KAAA,EAAuB;IAAA,EAAC;IAEnDzC,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAc,UAAA,mBAAA4B,yEAAA;MAAA1C,EAAA,CAAAiB,aAAA,CAAAmB,IAAA;MAAApC,EAAA,CAAAsB,aAAA;MAAA,MAAAgB,GAAA,GAAAtC,EAAA,CAAAuC,WAAA;MAAA,OAASvC,EAAA,CAAAuB,WAAA,CAAAe,GAAA,CAAAG,KAAA,EAAuB;IAAA,EAAC;IAEjCzC,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAYXH,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAuC,UAAA,CAAAC,KAAA,CAAsB;IACzE5C,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAS,WAAA,OAAAkC,UAAA,CAAAE,KAAA,OACF;;;;;;IAoBN7C,EAAA,CAAAC,cAAA,kCACkD;IADYD,EAAA,CAAAc,UAAA,qBAAAgC,4FAAAC,MAAA;MAAA,MAAA/B,WAAA,GAAAhB,EAAA,CAAAiB,aAAA,CAAA+B,IAAA;MAAA,MAAAC,WAAA,GAAAjC,WAAA,CAAAkC,aAAA;MAAA,OAAWlD,EAAA,CAAAuB,WAAA,CAAA0B,WAAA,CAAAF,MAAA,CAAe;IAAA,EAAC;IACvC/C,EAAA,CAAAG,YAAA,EAA0B;;;;;IADnDH,EAAA,CAAAI,UAAA,YAAA+C,OAAA,CAAAC,UAAA,CAAsB,SAAAC,QAAA;;;AD1GjD,OAAM,MAAOC,wBAAwB;EAoEnCC,YACSC,KAAiB,EACjBC,eAA+B,EAC/BC,iBAAmC,EACnCC,mBAAuC,EACtCC,cAA6B,EAC9BC,eAA+B,EAC/BC,oBAAyC,EACzCC,YAAyB,EACxBC,QAAqB,EACtBC,SAAiC;IATjC,KAAAT,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAClB,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,YAAY,GAAZA,YAAY;IACX,KAAAC,QAAQ,GAARA,QAAQ;IACT,KAAAC,SAAS,GAATA,SAAS;IA1ElB,KAAAC,SAAS,GAAQxE,kBAAkB;IACnC,KAAAyE,SAAS,GAAyB,IAAIvE,OAAO,EAAe;IAC5D,KAAAwE,SAAS,GAAQ,EAAE;IAIZ,KAAAC,MAAM,GAAQ,IAAI;IAClB,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,SAAS,GAAQ;MAAEC,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAI,CAAE;IACrD,KAAAC,cAAc,GAAW,EAAE;IAClC,KAAAlD,WAAW,GAAmB,IAAI;IAClC,KAAAmD,QAAQ,GAAmB,IAAI;IAC/B,KAAAC,MAAM,GAAmB,IAAI;IAC7B,KAAAC,WAAW,GAAY,KAAK;IAErB,KAAAC,WAAW,GAAW,KAAK;IAK3B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,UAAU,GAAG,YAAY;IAEhC,KAAAC,kBAAkB,GAAG,CACnB;MAAEpC,KAAK,EAAE,YAAY;MAAED,KAAK,EAAE;IAAK,CAAE,EACrC;MAAEC,KAAK,EAAE,UAAU;MAAED,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEC,KAAK,EAAE,QAAQ;MAAED,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAEC,KAAK,EAAE,WAAW;MAAED,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEC,KAAK,EAAE,WAAW;MAAED,KAAK,EAAE;IAAW,CAAE,CAC3C;IAEM,KAAAsC,MAAM,GAAwB;MACnCC,SAAS,EAAE,IAAI,CAACH,UAAU;MAC1BI,KAAK,EAAE;QACLC,MAAM,EAAE,IAAI,CAAC3B,iBAAiB,CAAC4B,OAAO,CAAC,uBAAuB,CAAC;QAC/DC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE;OACT;MACDC,GAAG,EAAE,GAAG9F,WAAW,CAAC+F,MAAM,eAAe;MACzCC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;KACT;IAEM,KAAAC,MAAM,GAAU,CACrB;MACEC,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLnD,KAAK,EAAE,IAAI,CAACa,iBAAiB,CAAC4B,OAAO,CAAC,iBAAiB,CAAC;QACxDW,WAAW,EAAE,IAAI,CAACvC,iBAAiB,CAAC4B,OAAO,CAAC,qBAAqB,CAAC;QAClEY,QAAQ,EAAE;;KAEb,EACD;MACEJ,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;QACLnD,KAAK,EAAE,IAAI,CAACa,iBAAiB,CAAC4B,OAAO,CAAC,iBAAiB,CAAC;QACxDW,WAAW,EAAE,IAAI,CAACvC,iBAAiB,CAAC4B,OAAO,CAAC,qBAAqB,CAAC;QAClEY,QAAQ,EAAE;;KAEb,CACF;EAaE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MACd,IAAI,CAACrC,SAAS,CAACsC,IAAI,CAAC,IAAI,CAACrC,SAAS,CAAC;IACrC,CAAC,EAAE,GAAG,CAAC;EACT;EAEAkC,cAAcA,CAAA;IAAA,IAAAI,KAAA;IACZ,IAAI,CAACtC,SAAS,GAAG;MACfuC,GAAG,EAAE,IAAI,CAAClD,eAAe,CAACmD,iBAAiB,CAACD,GAAG;MAC/CE,MAAM,EAAE,QAAQ;MAChBC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE;QACR,GAAG,IAAI,CAACvD,eAAe,CAACmD,iBAAiB,CAACK,IAAI;QAC9CF,UAAU,EAAE;OACb;MACDG,IAAI,EAAEA,CAACC,oBAAyB,EAAEC,QAAQ,KAAI;QAC5C;QACA,IAAIlC,MAAM,GAAG,IAAImC,eAAe,EAAE;QAElC,IAAI,IAAI,CAAChD,MAAM,IAAIiD,SAAS,IAAI,IAAI,CAACjD,MAAM,KAAK,IAAI,EAAE;UACpDa,MAAM,CAACqC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAClD,MAAM,CAAC;;QAGvC,IAAI,IAAI,CAACC,YAAY,IAAIgD,SAAS,IAAI,IAAI,CAAChD,YAAY,KAAK,IAAI,EAAE;UAChEY,MAAM,CAACqC,MAAM,CAAC,eAAe,EAAE,IAAI,CAACjD,YAAY,CAAC;;QAGnD,IAAI,IAAI,CAACC,SAAS,EAAEC,UAAU,EAAE;UAC9BU,MAAM,CAACqC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAChD,SAAS,CAACC,UAAU,CAAC;;QAGxD,IAAI,IAAI,CAACD,SAAS,EAAEE,QAAQ,EAAE;UAC5BS,MAAM,CAACqC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAChD,SAAS,CAACE,QAAQ,CAAC;;QAGpD,IAAI,IAAI,CAACK,WAAW,IAAI,IAAI,CAACA,WAAW,KAAK,KAAK,EAAE;UAClDI,MAAM,CAACqC,MAAM,CAAC,cAAc,EAAE,IAAI,CAACzC,WAAW,CAAC;;QAGjD,MAAM0C,WAAW,GAAGtC,MAAM,CAACuC,QAAQ,EAAE;QACrC,MAAMhC,GAAG,GAAG,GAAG9F,WAAW,CAAC+F,MAAM,YAAY,IAAI,CAACgC,QAAQ,WACxDF,WAAW,GAAG,GAAG,GAAGA,WAAW,GAAG,EACpC,EAAE;QAEF,IAAI,CAACzC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACvB,KAAK,CAACmE,IAAI,CAAMlC,GAAG,EAAE0B,oBAAoB,CAAC,CAACS,SAAS,CACtDC,IAAS,IAAI;UACZ,IAAI,CAAC9C,cAAc,GAAG,KAAK;UAC3BqC,QAAQ,CAAC;YACPU,YAAY,EAAED,IAAI,CAACC,YAAY;YAC/BC,eAAe,EAAEF,IAAI,CAACE,eAAe;YACrCC,IAAI,EAAEH,IAAI,CAACG;WACZ,CAAC;QACJ,CAAC,EACAC,KAAK,IAAI;UACR,IAAI,CAAClD,cAAc,GAAG,KAAK;UAC3BmD,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjDb,QAAQ,CAAC;YACPU,YAAY,EAAE,CAAC;YACfC,eAAe,EAAE,CAAC;YAClBC,IAAI,EAAE;WACP,CAAC;QACJ,CAAC,CACF;MACH,CAAC;MACDG,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,CAAC;QAAEC,kBAAkB,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;MAAC,CAAE,CAAC;MACpDC,OAAO,EAAE,CACP;QACEpD,KAAK,EAAE,IAAI,CAAC1B,iBAAiB,CAAC4B,OAAO,CAAC,KAAK,CAAC;QAC5C0C,IAAI,EAAE,IAAI;QACVS,SAAS,EAAE,oBAAoB;QAC/B1C,IAAI,EAAE,YAAY;QAClB2C,MAAM,EAAE,SAAAA,CAAUV,IAAI,EAAEjC,IAAI,EAAE4C,GAAG,EAAEC,QAAQ;UACzC,OAAOA,QAAQ,CAACD,GAAG,GAAG,CAAC;QACzB;OACD,EACD;QACEvD,KAAK,EAAE,IAAI,CAAC1B,iBAAiB,CAAC4B,OAAO,CAAC,YAAY,CAAC;QACnD0C,IAAI,EAAE,MAAM;QACZS,SAAS,EAAE,oBAAoB;QAC/B1C,IAAI,EAAE,YAAY;QAClB2C,MAAM,EAAE;UACNG,OAAO,EAAEA,CAACb,IAAI,EAAEjC,IAAI,EAAE4C,GAAG,KAAI;YAC3B,OAAOX,IAAI,IAAI,KAAK;UACtB,CAAC;UACDc,MAAM,EAAEA,CAACd,IAAI,EAAEjC,IAAI,EAAE4C,GAAG,KAAI;YAC1B,OAAOX,IAAI;UACb;;OAEH,EACD;QACE5C,KAAK,EAAE,IAAI,CAAC1B,iBAAiB,CAAC4B,OAAO,CAAC,OAAO,CAAC;QAC9C0C,IAAI,EAAE,YAAY;QAClBU,MAAM,EAAE,SAAAA,CAAUV,IAAI;UACpB,MAAMe,WAAW,GAAGf,IAAI,IAAI,KAAK;UACjC,OAAO,uDAAuDe,WAAW,QAAQ;QACnF;OACD,EACD;QACE3D,KAAK,EAAE,IAAI,CAAC1B,iBAAiB,CAAC4B,OAAO,CAAC,MAAM,CAAC;QAC7C0C,IAAI,EAAE,YAAY;QAClBS,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAAUV,IAAI,EAAEjC,IAAI,EAAE4C,GAAG;UAC/B,MAAMK,WAAW,GACf,CAAChB,IAAI,IAAIA,IAAI,KAAK,KAAK,GACnB,KAAK,GACLlI,MAAM,CAACkI,IAAI,CAAC,CAACiB,MAAM,CAAC,YAAY,CAAC;UACvC,OAAO,4DAA4DD,WAAW,QAAQ;QACxF;OACD,EACD;QACE5D,KAAK,EAAE,IAAI,CAAC1B,iBAAiB,CAAC4B,OAAO,CAAC,YAAY,CAAC;QACnD0C,IAAI,EAAE,YAAY;QAClBS,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAASV,IAAI,EAAEjC,IAAI,EAAE4C,GAAG;UAC9B,IAAI,CAACX,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd,OAAOlI,MAAM,CAACkI,IAAI,CAAC,CAACiB,MAAM,CAAC,OAAO,CAAC;QACrC;OACD,EACD;QACE7D,KAAK,EAAE,IAAI,CAAC1B,iBAAiB,CAAC4B,OAAO,CAAC,UAAU,CAAC;QACjD0C,IAAI,EAAE,UAAU;QAChBS,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAASV,IAAI,EAAEjC,IAAI,EAAE4C,GAAG;UAC9B,IAAI,CAACX,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd,OAAOlI,MAAM,CAACkI,IAAI,CAAC,CAACiB,MAAM,CAAC,OAAO,CAAC;QACrC;OACD,EACD;QACE7D,KAAK,EAAE,IAAI,CAAC1B,iBAAiB,CAAC4B,OAAO,CAAC,UAAU,CAAC;QACjD0C,IAAI,EAAE,UAAU;QAChBU,MAAM,EAAE,SAAAA,CAASV,IAAI,EAAEjC,IAAI,EAAE4C,GAAG;UAC9B,IAAI,CAACX,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAE;YAC1B,OAAO,KAAK;;UAEd,OAAOA,IAAI;QACb;OACD,EACD;QACE5C,KAAK,EAAE,IAAI,CAAC1B,iBAAiB,CAAC4B,OAAO,CAAC,WAAW,CAAC;QAClD0C,IAAI,EAAE,gBAAgB;QACtBS,SAAS,EAAE;OACZ,EACD;QACErD,KAAK,EAAE,IAAI;QACX4C,IAAI,EAAE,IAAI;QACVS,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAASV,IAAI,EAAEjC,IAAI,EAAE4C,GAAG;UAC9B,OAAO,IAAI;QACb;OACD,EACD;QACEvD,KAAK,EAAE,IAAI,CAAC1B,iBAAiB,CAAC4B,OAAO,CAAC,WAAW,CAAC;QAClD0C,IAAI,EAAE,gBAAgB;QACtBS,SAAS,EAAE;OACZ,EACD;QACErD,KAAK,EAAE,IAAI,CAAC1B,iBAAiB,CAAC4B,OAAO,CAAC,YAAY,CAAC;QACnD0C,IAAI,EAAE,YAAY;QAClBS,SAAS,EAAE;OACZ,EACD;QACET,IAAI,EAAE,IAAI;QACVS,SAAS,EAAE,aAAa;QACxBC,MAAM,EAAE,SAAAA,CAASV,IAAI,EAAEjC,IAAI,EAAE4C,GAAG;UAC9B,OAAO,GAAG;QACZ;OACD,EACD;QACEvD,KAAK,EAAE,IAAI,CAAC1B,iBAAiB,CAAC4B,OAAO,CAAC,YAAY,CAAC;QACnD0C,IAAI,EAAE,YAAY;QAClBS,SAAS,EAAE;OACZ,CACF;MACDS,OAAO,EAAE;QACPvC,GAAG,EAAE,IAAI,CAAClD,eAAe,CAACmD,iBAAiB,CAACsC,OAAO,CAACvC,GAAG;QACvDuC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,6CAA6C,IAAI,CAACzF,iBAAiB,CAAC4B,OAAO,CAC/E,YAAY,CACb,EAAE;UACH8D,MAAM,EAAE,KAAK;UACbxD,MAAM;YAAA,IAAAyD,IAAA,GAAAC,iBAAA,CAAE,WAAOC,CAAM,EAAEC,EAAO,EAAEC,MAAW,EAAEC,MAAW,EAAI;cAC1D,MAAM1B,IAAI,GAAGwB,EAAE,CAACN,OAAO,CAACS,UAAU,EAAE;cAEpC;cACA,IAAIC,QAAQ,GAAG,gBAAgB;cAE/B,IAAIlD,KAAI,CAACgB,QAAQ,EAAE;gBACjB,MAAMmC,MAAM,GAAGnD,KAAI,CAACoD,OAAO,EAAEC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1J,EAAE,IAAIoG,KAAI,CAACgB,QAAQ,CAAC;gBAC7D,IAAImC,MAAM,EAAE;kBACVD,QAAQ,IAAI,IAAIC,MAAM,CAACnJ,IAAI,CAACuJ,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAItD,IAAIvD,KAAI,CAACpC,YAAY,EAAE;gBACrB,MAAM4F,UAAU,GAAGxD,KAAI,CAACyD,WAAW,EAAEJ,IAAI,CAACK,CAAC,IAAIA,CAAC,CAAC9J,EAAE,IAAIoG,KAAI,CAACpC,YAAY,CAAC;gBACzE,IAAI4F,UAAU,EAAE;kBACdN,QAAQ,IAAI,IAAIM,UAAU,CAACxJ,IAAI,CAACuJ,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAI1D,IAAIvD,KAAI,CAACrC,MAAM,EAAE;gBACf,MAAMgG,IAAI,GAAG3D,KAAI,CAAC4D,KAAK,EAAEP,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAACjK,EAAE,IAAIoG,KAAI,CAACrC,MAAM,CAAC;gBACvD,IAAIgG,IAAI,EAAE;kBACRT,QAAQ,IAAI,IAAIS,IAAI,CAACxJ,IAAI,CAACoJ,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;;;cAIpD,IAAIvD,KAAI,CAAC5B,WAAW,IAAI4B,KAAI,CAAC5B,WAAW,KAAK,KAAK,EAAE;gBAClD8E,QAAQ,IAAI,IAAIlD,KAAI,CAAC5B,WAAW,EAAE;;cAGpC,IAAI4B,KAAI,CAACnC,SAAS,EAAEC,UAAU,IAAIkC,KAAI,CAACnC,SAAS,EAAEE,QAAQ,EAAE;gBAC1DmF,QAAQ,IAAI,IAAIlD,KAAI,CAACnC,SAAS,CAACC,UAAU,OAAOkC,KAAI,CAACnC,SAAS,CAACE,QAAQ,EAAE;eAC1E,MAAM,IAAIiC,KAAI,CAACnC,SAAS,EAAEC,UAAU,EAAE;gBACrCoF,QAAQ,IAAI,SAASlD,KAAI,CAACnC,SAAS,CAACC,UAAU,EAAE;;cAGlDoF,QAAQ,IAAI,MAAM;cAElB,MAAMlD,KAAI,CAAC9C,cAAc,CAAC4G,SAAS,CAACxC,IAAI,EAAE4B,QAAQ,CAAC;YACrD,CAAC;YAAA,gBAAAhE,OAAA6E,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;cAAA,OAAAvB,IAAA,CAAAwB,KAAA,OAAAC,SAAA;YAAA;UAAA;SACF;;KAGN;EACH;EAEAC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC7G,SAAS,EAAE8G,UAAU,EAAE;MAC9B,IAAI,CAAC9G,SAAS,CAAC8G,UAAU,CAACC,IAAI,CAAED,UAA0B,IAAI;QAC5DA,UAAU,CAAC9D,IAAI,CAACgE,MAAM,EAAE;MAC1B,CAAC,CAAC;;EAEN;EAEA;EACA9E,iBAAiBA,CAAA;IACf,IAAI,CAACvC,eAAe,CAACsH,IAAI,EAAE;IAC3B,IAAI,CAACrH,oBAAoB,CAACsH,kBAAkB,EAAE,CAACxD,SAAS,CACrDI,IAAI,IAAI;MACP,IAAI,CAAC8B,OAAO,GAAG9B,IAAI;MACnB,IAAI,CAACN,QAAQ,GAAG,IAAI,CAACoC,OAAO,CAAC,CAAC,CAAC,CAACxJ,EAAE;MAClC,IAAI,CAAC+K,eAAe,EAAE;IACxB,CAAC,EACApD,KAAK,IAAI;MACRlI,IAAI,CAACuL,IAAI,CAAC;QACRlG,KAAK,EAAE,OAAO;QACd+D,IAAI,EAAElB,KAAK,CAACsD,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAAC/H,iBAAiB,CAAC4B,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,EACD,MAAK;MACH,IAAI,CAACzB,eAAe,CAAC6H,OAAO,EAAE;IAChC,CAAC,CACF;EACH;EAEArF,SAASA,CAAA;IACP,IAAI,CAACtC,YAAY,CAAC4H,WAAW,EAAE,CAAC/D,SAAS,CACtCgE,GAAG,IAAI;MACN,IAAI,CAACtB,KAAK,GAAGsB,GAAG,CAAC5D,IAAI;IACvB,CAAC,EACAC,KAAK,IAAI;MACRlI,IAAI,CAACuL,IAAI,CAAC;QACRlG,KAAK,EAAE,OAAO;QACd+D,IAAI,EAAElB,KAAK,CAACsD,OAAO;QACnBC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE,IAAI,CAAC/H,iBAAiB,CAAC4B,OAAO,CAAC,IAAI;OACvD,CAAC;IACJ,CAAC,CACF;EACH;EAEA+F,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC3D,QAAQ,EAAE;MACjB,IAAI,CAAClE,KAAK,CACPqI,GAAG,CACF,GAAGlM,WAAW,CAAC+F,MAAM,YAAY,IAAI,CAACgC,QAAQ,+BAA+B,CAC9E,CACAE,SAAS,CACPI,IAAI,IAAI;QACP,IAAI,CAACmC,WAAW,GAAGnC,IAAI,CAACA,IAAI;MAC9B,CAAC,EACAC,KAAK,IAAI;QACRlI,IAAI,CAACuL,IAAI,CAAC;UACRlG,KAAK,EAAE,OAAO;UACd+D,IAAI,EAAElB,KAAK,CAACsD,OAAO;UACnBC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE,IAAI,CAAC/H,iBAAiB,CAAC4B,OAAO,CAAC,IAAI;SACvD,CAAC;MACJ,CAAC,CACF;;EAEP;EAEAwG,cAAcA,CAAC/I,MAAW;IACxB,OAAO,IAAIgJ,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAI,CAACvE,QAAQ,GAAG3E,MAAM;MACtB,IAAI,CAACsI,eAAe,EAAE;MACtB,IAAI,CAAC/G,YAAY,GAAG,IAAI;MACxB,IAAI,CAACyG,gBAAgB,EAAE;MACvBiB,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;EACJ;EAEAE,YAAYA,CAACnJ,MAAW;IACtB,IAAI,CAACsB,MAAM,GAAGtB,MAAM;IACpB,IAAI,CAACgI,gBAAgB,EAAE;EACzB;EAEAoB,kBAAkBA,CAACpJ,MAAW;IAC5B,IAAI,CAACuB,YAAY,GAAGvB,MAAM;IAC1B,IAAI,CAACgI,gBAAgB,EAAE;EACzB;EAEAqB,mBAAmBA,CAACrJ,MAAW;IAC7B,IAAI,CAAC+B,WAAW,GAAG/B,MAAM;IACzB,IAAI,CAACgI,gBAAgB,EAAE;EACzB;EAEA;EACAsB,eAAeA,CAACjL,IAAa;IAC3B,IAAI,CAAC,IAAI,CAACuD,QAAQ,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MAClC,IAAI,CAACD,QAAQ,GAAGvD,IAAI;MACpB,IAAI,CAACyD,WAAW,GAAG,IAAI;KACxB,MAAM,IACL,IAAI,CAACF,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZxD,IAAI,IACJA,IAAI,CAACkL,KAAK,CAAC,IAAI,CAAC3H,QAAQ,CAAC,EACzB;MACA,IAAI,CAACC,MAAM,GAAGxD,IAAI;MAClB,IAAI,CAACyD,WAAW,GAAG,KAAK;MACxB,IAAI,CAAC0H,eAAe,EAAE;MACtB/F,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACgG,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC/J,KAAK,EAAE;UACtD,IAAI,CAAC+J,eAAe,CAAC/J,KAAK,EAAE;;MAEhC,CAAC,EAAE,CAAC,CAAC;KACN,MAAM;MACL,IAAI,CAACmC,MAAM,GAAG,IAAI;MAClB,IAAI,CAACD,QAAQ,GAAGvD,IAAI;MACpB,IAAI,CAACyD,WAAW,GAAG,IAAI;;EAE3B;EAEA4H,cAAcA,CAAA;IACZ,IAAI,CAAC5H,WAAW,GAAG,KAAK;IACxB,IAAI,IAAI,CAAC2H,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACE,IAAI,EAAE;;EAE/B;EAGAC,eAAeA,CAACC,KAAY;IAC1B,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;IAGxC,IAAI,CAACA,mBAAmB,GAAGrG,UAAU,CAAC,MAAK;MACzC,IAAI,CAACuG,kBAAkB,CAACH,KAAK,CAAC;IAChC,CAAC,EAAE,EAAE,CAAC;EACR;EAEQG,kBAAkBA,CAACH,KAAY;IACrC,IACE,CAAC,IAAI,CAACJ,eAAe,IACrB,CAAC,IAAI,CAACA,eAAe,CAACQ,MAAM,IAC5B,CAAC,IAAI,CAACR,eAAe,CAACQ,MAAM,EAAE,EAC9B;MACA;;IAGF,IAAI,IAAI,CAACnI,WAAW,EAAE;MACpB;;IAGF,MAAMoI,MAAM,GAAGL,KAAK,CAACK,MAAqB;IAC1C,IAAI,CAACA,MAAM,EAAE;IAEb,IACEA,MAAM,CAACC,OAAO,CAAC,MAAM,CAAC,IACtBD,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,SAAS,CAAC,IACpCH,MAAM,CAACE,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,EAC1C;MACA;;IAGF,IACEH,MAAM,CAACI,OAAO,KAAK,OAAO,IAC1BJ,MAAM,CAACK,YAAY,CAAC,MAAM,CAAC,KAAK,WAAW,EAC3C;MACA;;IAGF,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;IAClE,IAAIF,iBAAiB,IAAIA,iBAAiB,CAACH,QAAQ,CAACH,MAAM,CAAC,EAAE;MAC3D;;IAGF,IAAI,CAACT,eAAe,CAAC/J,KAAK,EAAE;EAC9B;EAEAD,cAAcA,CAAA;IACZ,IAAI,CAACmC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC0H,eAAe,EAAE;EACxB;EAEAmB,aAAaA,CAACC,YAA4B,EAAEC,KAAa;IACvD,MAAMC,MAAM,GAAG,IAAI,CAAC5J,SAAS,CAAC6J,KAAK,CAACF,KAAK,CAAC;IAC1C,OAAOC,MAAM,IAAI,IAAI,CAAC7J,QAAQ,CAAC+J,OAAO,CAAClO,OAAO,CAACmO,IAAI,CAACH,MAAM,CAAC,CAAC,GACxDhO,OAAO,CAACmO,IAAI,CAACH,MAAM,CAAC,GACpBF,YAAY;EAClB;EAEA5L,SAASA,CAACX,IAAa;IACrB,OACE,IAAI,CAACuD,QAAQ,IACb,CAAC,IAAI,CAACC,MAAM,IACZ,IAAI,CAACpD,WAAW,IAChBJ,IAAI,CAACkL,KAAK,CAAC,IAAI,CAAC3H,QAAQ,CAAC,IACzBvD,IAAI,CAAC6M,MAAM,CAAC,IAAI,CAACzM,WAAW,CAAC;EAEjC;EAEAQ,QAAQA,CAACZ,IAAa;IACpB,OAAO,IAAI,CAACwD,MAAM,IAAIxD,IAAI,CAACkL,KAAK,CAAC,IAAI,CAAC3H,QAAQ,CAAC,IAAIvD,IAAI,CAAC6M,MAAM,CAAC,IAAI,CAACrJ,MAAM,CAAC;EAC7E;EAEA9C,OAAOA,CAACV,IAAa;IACnB,OACEA,IAAI,CAAC8M,MAAM,CAAC,IAAI,CAACvJ,QAAQ,CAAC,IACzB,IAAI,CAACC,MAAM,IAAIxD,IAAI,CAAC8M,MAAM,CAAC,IAAI,CAACtJ,MAAM,CAAE,IACzC,IAAI,CAAC5C,QAAQ,CAACZ,IAAI,CAAC,IACnB,IAAI,CAACW,SAAS,CAACX,IAAI,CAAC;EAExB;EAEA+M,iBAAiBA,CAAA;IACf,IAAI,CAACpD,gBAAgB,EAAE;EACzB;EAEAwB,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC5H,QAAQ,IAAI,IAAI,CAACC,MAAM,EAAE;MAChC,MAAMwJ,UAAU,GAAGtO,MAAM,CACvB,GAAG,IAAI,CAAC6E,QAAQ,CAAC0J,IAAI,IAAI,IAAI,CAAC1J,QAAQ,CAAC2J,KAAK,CACzC7G,QAAQ,EAAE,CACV8G,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC5J,QAAQ,CAAC1C,GAAG,CAACwF,QAAQ,EAAE,CAAC8G,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,MAAMC,QAAQ,GAAG1O,MAAM,CACrB,GAAG,IAAI,CAAC8E,MAAM,CAACyJ,IAAI,IAAI,IAAI,CAACzJ,MAAM,CAAC0J,KAAK,CACrC7G,QAAQ,EAAE,CACV8G,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC3J,MAAM,CAAC3C,GAAG,CAACwF,QAAQ,EAAE,CAAC8G,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACrE;MAED,IAAI,CAAChK,SAAS,CAACC,UAAU,GAAG4J,UAAU,CAACnF,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAAC1E,SAAS,CAACE,QAAQ,GAAG+J,QAAQ,CAACvF,MAAM,CAAC,YAAY,CAAC;MACvD,IAAI,CAACvE,cAAc,GAAG,GAAG0J,UAAU,CAACnF,MAAM,CACxC,cAAc,CACf,MAAMuF,QAAQ,CAACvF,MAAM,CAAC,cAAc,CAAC,EAAE;MAExC,IAAI,CAACkF,iBAAiB,EAAE;KACzB,MAAM,IAAI,IAAI,CAACxJ,QAAQ,EAAE;MACxB,MAAMyJ,UAAU,GAAGtO,MAAM,CACvB,GAAG,IAAI,CAAC6E,QAAQ,CAAC0J,IAAI,IAAI,IAAI,CAAC1J,QAAQ,CAAC2J,KAAK,CACzC7G,QAAQ,EAAE,CACV8G,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC5J,QAAQ,CAAC1C,GAAG,CAACwF,QAAQ,EAAE,CAAC8G,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACvE;MACD,IAAI,CAAChK,SAAS,CAACC,UAAU,GAAG4J,UAAU,CAACnF,MAAM,CAAC,YAAY,CAAC;MAC3D,IAAI,CAAC1E,SAAS,CAACE,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAG0J,UAAU,CAACnF,MAAM,CAAC,cAAc,CAAC;KACxD,MAAM;MACL,IAAI,CAAC1E,SAAS,CAACC,UAAU,GAAG,IAAI;MAChC,IAAI,CAACD,SAAS,CAACE,QAAQ,GAAG,IAAI;MAC9B,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACyJ,iBAAiB,EAAE;;EAE5B;EAEAM,WAAWA,CAAA;IACT,IAAI,CAACtK,SAAS,CAACuK,WAAW,EAAE;IAC5B,IAAI,IAAI,CAAC7B,mBAAmB,EAAE;MAC5BC,YAAY,CAAC,IAAI,CAACD,mBAAmB,CAAC;;EAE1C;EAAC,QAAA8B,CAAA;qBAvkBUrL,wBAAwB,EAAAtD,EAAA,CAAA4O,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAA9O,EAAA,CAAA4O,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAhP,EAAA,CAAA4O,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAlP,EAAA,CAAA4O,iBAAA,CAAAO,EAAA,CAAAC,kBAAA,GAAApP,EAAA,CAAA4O,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAtP,EAAA,CAAA4O,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAxP,EAAA,CAAA4O,iBAAA,CAAAa,EAAA,CAAAC,mBAAA,GAAA1P,EAAA,CAAA4O,iBAAA,CAAAe,EAAA,CAAAC,WAAA,GAAA5P,EAAA,CAAA4O,iBAAA,CAAAiB,EAAA,CAAAC,WAAA,GAAA9P,EAAA,CAAA4O,iBAAA,CAAAiB,EAAA,CAAAE,sBAAA;EAAA;EAAA,QAAAC,EAAA;UAAxB1M,wBAAwB;IAAA2M,SAAA;IAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;uBAGxB1Q,kBAAkB;;;;;;;;;;;;iBAHlB2Q,GAAA,CAAA1D,eAAA,CAAA5J,MAAA,CAAuB;QAAA,UAAA/C,EAAA,CAAAsQ,iBAAA;;;;;;;;QC/BpCtQ,EAAA,CAAAC,cAAA,aAAiB;QAI4BD,EAAA,CAAAE,MAAA,GAAsB;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACrEH,EAAA,CAAAC,cAAA,mBAKoC;QADlCD,EAAA,CAAAc,UAAA,2BAAAyP,qEAAAxN,MAAA;UAAA,OAAAsN,GAAA,CAAA3I,QAAA,GAAA3E,MAAA;QAAA,EAAsB,oBAAAyN,8DAAAzN,MAAA;UAAA,OACZsN,GAAA,CAAAvE,cAAA,CAAA/I,MAAA,CAAsB;QAAA,EADV;;QAEtB/C,EAAA,CAAAyQ,UAAA,IAAAC,6CAAA,uBAEY;QACd1Q,EAAA,CAAAG,YAAA,EAAY;QAGhBH,EAAA,CAAAC,cAAA,aAAsB;QAEyBD,EAAA,CAAAE,MAAA,IAA0B;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC7EH,EAAA,CAAAC,cAAA,oBAKwC;QADtCD,EAAA,CAAAc,UAAA,2BAAA6P,sEAAA5N,MAAA;UAAA,OAAAsN,GAAA,CAAA/L,YAAA,GAAAvB,MAAA;QAAA,EAA0B,oBAAA6N,+DAAA7N,MAAA;UAAA,OAChBsN,GAAA,CAAAlE,kBAAA,CAAApJ,MAAA,CAA0B;QAAA,EADV;;QAE1B/C,EAAA,CAAAyQ,UAAA,KAAAI,8CAAA,uBAEY;QACd7Q,EAAA,CAAAG,YAAA,EAAY;QAEdH,EAAA,CAAAC,cAAA,cAAsB;QACiBD,EAAA,CAAAE,MAAA,IAAoB;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACjEH,EAAA,CAAAC,cAAA,oBAKkC;QADhCD,EAAA,CAAAc,UAAA,2BAAAgQ,sEAAA/N,MAAA;UAAA,OAAAsN,GAAA,CAAAhM,MAAA,GAAAtB,MAAA;QAAA,EAAoB,oBAAAgO,+DAAAhO,MAAA;UAAA,OACVsN,GAAA,CAAAnE,YAAA,CAAAnJ,MAAA,CAAoB;QAAA,EADV;;QAEpB/C,EAAA,CAAAyQ,UAAA,KAAAO,8CAAA,uBAEY;QACdhR,EAAA,CAAAG,YAAA,EAAY;QAEdH,EAAA,CAAAC,cAAA,cAAsB;QACsBD,EAAA,CAAAE,MAAA,IAA0B;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC5EH,EAAA,CAAAC,cAAA,eAAyB;QASrBD,EAAA,CAAAc,UAAA,mBAAAmQ,0DAAA;UAAA,OAASZ,GAAA,CAAA5D,cAAA,EAAgB;QAAA,EAAC,wBAAAyE,+DAAAnO,MAAA;UAAA,OAGZsN,GAAA,CAAAhE,eAAA,CAAAtJ,MAAA,CAAuB;QAAA,EAHX;;QAR5B/C,EAAA,CAAAG,YAAA,EAgBE;QACFH,EAAA,CAAAC,cAAA,eAAgC;QAG5BD,EAAA,CAAAc,UAAA,mBAAAqQ,2DAAA;UAAA,OAASd,GAAA,CAAA5D,cAAA,EAAgB;QAAA,EAAC;QAE1BzM,EAAA,CAAAkC,SAAA,aAAqC;QACvClC,EAAA,CAAAG,YAAA,EAAS;QAIbH,EAAA,CAAAyQ,UAAA,KAAAW,gDAAA,iCAAApR,EAAA,CAAAqR,sBAAA,CAWc;QAEdrR,EAAA,CAAAyQ,UAAA,KAAAa,gDAAA,iCAAAtR,EAAA,CAAAqR,sBAAA,CAkBc;QAChBrR,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,cAAsB;QACwBD,EAAA,CAAAE,MAAA,IAA4B;;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAChFH,EAAA,CAAAC,cAAA,oBAKyC;QADvCD,EAAA,CAAAc,UAAA,2BAAAyQ,sEAAAxO,MAAA;UAAA,OAAAsN,GAAA,CAAAvL,WAAA,GAAA/B,MAAA;QAAA,EAAyB,oBAAAyO,+DAAAzO,MAAA;UAAA,OACfsN,GAAA,CAAAjE,mBAAA,CAAArJ,MAAA,CAA2B;QAAA,EADZ;;QAEzB/C,EAAA,CAAAyQ,UAAA,KAAAgB,8CAAA,uBAEY;QACdzR,EAAA,CAAAG,YAAA,EAAY;QAKhBH,EAAA,CAAAC,cAAA,cAAsB;QAElBD,EAAA,CAAAkC,SAAA,iBACQ;QACVlC,EAAA,CAAAG,YAAA,EAAM;QAIVH,EAAA,CAAAC,cAAA,wBAAqH;QACnHD,EAAA,CAAAkC,SAAA,8BACqB;QACvBlC,EAAA,CAAAG,YAAA,EAAe;QAEfH,EAAA,CAAAyQ,UAAA,KAAAiB,gDAAA,iCAAA1R,EAAA,CAAAqR,sBAAA,CAGc;;;;;QAvI+BrR,EAAA,CAAAO,SAAA,GAAsB;QAAtBP,EAAA,CAAA2R,iBAAA,CAAA3R,EAAA,CAAAS,WAAA,kBAAsB;QAI3DT,EAAA,CAAAO,SAAA,GAA2C;QAA3CP,EAAA,CAAA4R,qBAAA,gBAAA5R,EAAA,CAAAS,WAAA,yBAA2C;QAF3CT,EAAA,CAAAI,UAAA,oBAAmB,gCAAAiQ,GAAA,CAAA3I,QAAA;QAKW1H,EAAA,CAAAO,SAAA,GAAU;QAAVP,EAAA,CAAAI,UAAA,YAAAiQ,GAAA,CAAAvG,OAAA,CAAU;QAQC9J,EAAA,CAAAO,SAAA,GAA0B;QAA1BP,EAAA,CAAA2R,iBAAA,CAAA3R,EAAA,CAAAS,WAAA,uBAA0B;QAInET,EAAA,CAAAO,SAAA,GAA+C;QAA/CP,EAAA,CAAA4R,qBAAA,gBAAA5R,EAAA,CAAAS,WAAA,8BAA+C;QAF/CT,EAAA,CAAAI,UAAA,oBAAmB,+BAAAiQ,GAAA,CAAA/L,YAAA;QAKetE,EAAA,CAAAO,SAAA,GAAc;QAAdP,EAAA,CAAAI,UAAA,YAAAiQ,GAAA,CAAAlG,WAAA,CAAc;QAMbnK,EAAA,CAAAO,SAAA,GAAoB;QAApBP,EAAA,CAAA2R,iBAAA,CAAA3R,EAAA,CAAAS,WAAA,iBAAoB;QAIvDT,EAAA,CAAAO,SAAA,GAAyC;QAAzCP,EAAA,CAAA4R,qBAAA,gBAAA5R,EAAA,CAAAS,WAAA,wBAAyC;QAFzCT,EAAA,CAAAI,UAAA,oBAAmB,+BAAAiQ,GAAA,CAAAhM,MAAA;QAKSrE,EAAA,CAAAO,SAAA,GAAQ;QAARP,EAAA,CAAAI,UAAA,YAAAiQ,GAAA,CAAA/F,KAAA,CAAQ;QAMItK,EAAA,CAAAO,SAAA,GAA0B;QAA1BP,EAAA,CAAA2R,iBAAA,CAAA3R,EAAA,CAAAS,WAAA,uBAA0B;QAKhET,EAAA,CAAAO,SAAA,GAA+C;QAA/CP,EAAA,CAAA4R,qBAAA,gBAAA5R,EAAA,CAAAS,WAAA,8BAA+C;QAI/CT,EAAA,CAAAI,UAAA,UAAAiQ,GAAA,CAAApM,SAAA,CAAAgF,MAAA,CAAAoH,GAAA,CAAA1L,QAAA,KAAA0L,GAAA,CAAAzL,MAAA,WAAAyL,GAAA,CAAApM,SAAA,CAAAgF,MAAA,CAAAoH,GAAA,CAAAzL,MAAA,QAAuF,gBAAAiN,GAAA,oBAAAC,GAAA;QAsD/C9R,EAAA,CAAAO,SAAA,IAA4B;QAA5BP,EAAA,CAAA2R,iBAAA,CAAA3R,EAAA,CAAAS,WAAA,yBAA4B;QAItET,EAAA,CAAAO,SAAA,GAA2C;QAA3CP,EAAA,CAAA4R,qBAAA,gBAAA5R,EAAA,CAAAS,WAAA,0BAA2C;QAF3CT,EAAA,CAAAI,UAAA,qBAAoB,gCAAAiQ,GAAA,CAAAvL,WAAA;QAKU9E,EAAA,CAAAO,SAAA,GAAqB;QAArBP,EAAA,CAAAI,UAAA,YAAAiQ,GAAA,CAAApL,kBAAA,CAAqB;QAUpCjF,EAAA,CAAAO,SAAA,GAAuB;QAAvBP,EAAA,CAAAI,UAAA,cAAAiQ,GAAA,CAAAjM,SAAA,CAAuB,cAAAiM,GAAA,CAAAlM,SAAA;QAMqBnE,EAAA,CAAAO,SAAA,GAAmB;QAAnBP,EAAA,CAAAI,UAAA,SAAAiQ,GAAA,CAAArL,UAAA,CAAmB;QAChEhF,EAAA,CAAAO,SAAA,GAAmB;QAAnBP,EAAA,CAAAI,UAAA,UAAAiQ,GAAA,CAAAnM,SAAA,CAAmB,WAAAmM,GAAA,CAAAxK,MAAA,YAAAwK,GAAA,CAAAnL,MAAA", "names": ["DataTableDirective", "environment", "Subject", "NgbDate", "moment", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "season_r11", "id", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "name", "tournament_r12", "club_r13", "code", "ɵɵlistener", "LeagueTableViewComponent_ng_template_35_Template_span_mouseenter_0_listener", "restoredCtx", "ɵɵrestoreView", "_r17", "date_r14", "date", "ctx_r16", "ɵɵnextContext", "ɵɵresetView", "hoveredDate", "LeagueTableViewComponent_ng_template_35_Template_span_mouseleave_0_listener", "ctx_r18", "ɵɵclassProp", "focused_r15", "ctx_r5", "isRange", "isHovered", "isInside", "day", "ɵɵelement", "LeagueTableViewComponent_ng_template_37_Template_button_click_2_listener", "_r20", "ctx_r19", "_r3", "ɵɵreference", "clearDateRange", "close", "LeagueTableViewComponent_ng_template_37_Template_button_click_4_listener", "status_r22", "value", "label", "LeagueTableViewComponent_ng_template_51_Template_app_btn_dropdown_action_emitter_0_listener", "$event", "_r26", "emitter_r24", "captureEvents", "ctx_r10", "rowActions", "data_r23", "LeagueTableViewComponent", "constructor", "_http", "_commonsService", "_translateService", "_coreSidebarService", "_exportService", "_loadingService", "_registrationService", "_clubService", "calendar", "formatter", "dtElement", "dtTrigger", "dtOptions", "clubId", "tournamentId", "date<PERSON><PERSON><PERSON>", "start_date", "end_date", "dateRangeValue", "fromDate", "toDate", "isSelecting", "matchStatus", "isTableLoading", "table_name", "matchStatusOptions", "params", "editor_id", "title", "create", "instant", "edit", "remove", "url", "apiUrl", "method", "action", "fields", "key", "type", "props", "placeholder", "required", "ngOnInit", "_getCurrentSeason", "_getClubs", "buildDataTable", "ngAfterViewInit", "setTimeout", "next", "_this", "dom", "dataTableDefaults", "select", "rowId", "processing", "language", "lang", "ajax", "dataTablesParameters", "callback", "URLSearchParams", "undefined", "append", "queryString", "toString", "seasonId", "post", "subscribe", "resp", "recordsTotal", "recordsFiltered", "data", "error", "console", "responsive", "scrollX", "columnDefs", "responsivePriority", "targets", "columns", "className", "render", "row", "metadata", "display", "filter", "displayData", "displayDate", "format", "buttons", "text", "extend", "_ref", "_asyncToGenerator", "e", "dt", "button", "config", "exportData", "filename", "season", "seasons", "find", "s", "replace", "tournament", "tournaments", "t", "club", "clubs", "c", "exportCsv", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "refreshDataTable", "dtInstance", "then", "reload", "show", "getAllSeasonActive", "_getTournaments", "fire", "message", "icon", "confirmButtonText", "dismiss", "getAllClubs", "res", "get", "onSelectSeason", "Promise", "resolve", "reject", "onSelectClub", "onSelectTournament", "onSelectMatchStatus", "onDateSelection", "after", "updateDateRange", "dateRangePicker", "openDatePicker", "open", "onDocumentClick", "event", "clickOutsideTimeout", "clearTimeout", "handleClickOutside", "isOpen", "target", "closest", "classList", "contains", "tagName", "getAttribute", "datepickerElement", "document", "querySelector", "validateInput", "currentValue", "input", "parsed", "parse", "<PERSON><PERSON><PERSON><PERSON>", "from", "before", "equals", "onDateRangeChange", "fromMoment", "year", "month", "padStart", "toMoment", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "CommonsService", "i3", "TranslateService", "i4", "CoreSidebarService", "i5", "ExportService", "i6", "LoadingService", "i7", "RegistrationService", "i8", "ClubService", "i9", "NgbCalendar", "NgbDateParserFormatter", "_2", "selectors", "viewQuery", "LeagueTableViewComponent_Query", "rf", "ctx", "ɵɵresolveDocument", "LeagueTableViewComponent_Template_ng_select_ngModelChange_6_listener", "LeagueTableViewComponent_Template_ng_select_change_6_listener", "ɵɵtemplate", "LeagueTableViewComponent_ng_option_8_Template", "LeagueTableViewComponent_Template_ng_select_ngModelChange_14_listener", "LeagueTableViewComponent_Template_ng_select_change_14_listener", "LeagueTableViewComponent_ng_option_16_Template", "LeagueTableViewComponent_Template_ng_select_ngModelChange_21_listener", "LeagueTableViewComponent_Template_ng_select_change_21_listener", "LeagueTableViewComponent_ng_option_23_Template", "LeagueTableViewComponent_Template_input_click_29_listener", "LeagueTableViewComponent_Template_input_dateSelect_29_listener", "LeagueTableViewComponent_Template_button_click_33_listener", "LeagueTableViewComponent_ng_template_35_Template", "ɵɵtemplateRefExtractor", "LeagueTableViewComponent_ng_template_37_Template", "LeagueTableViewComponent_Template_ng_select_ngModelChange_43_listener", "LeagueTableViewComponent_Template_ng_select_change_43_listener", "LeagueTableViewComponent_ng_option_45_Template", "LeagueTableViewComponent_ng_template_51_Template", "ɵɵtextInterpolate", "ɵɵpropertyInterpolate", "_r4", "_r6"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-table-view\\league-table-view.component.ts", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-table-view\\league-table-view.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\nimport {\n  Component,\n  OnInit,\n  ViewChild,\n  AfterViewInit,\n  OnDestroy,\n  TemplateRef,\n  HostListener\n} from '@angular/core';\nimport { TranslateService } from '@ngx-translate/core';\nimport { DataTableDirective } from 'angular-datatables';\nimport { CommonsService } from 'app/services/commons.service';\nimport { ExportService } from 'app/services/export.service';\nimport { LoadingService } from 'app/services/loading.service';\nimport { RegistrationService } from 'app/services/registration.service';\nimport { ClubService } from 'app/services/club.service';\nimport { environment } from 'environments/environment';\nimport { Subject } from 'rxjs';\nimport { ADTSettings } from 'angular-datatables/src/models/settings';\nimport { EditorSidebarParams } from 'app/interfaces/editor-sidebar';\nimport { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';\nimport { NgbCalendar, NgbDate, NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';\nimport moment from 'moment';\nimport Swal from 'sweetalert2';\n\n@Component({\n  selector: 'app-league-table-view',\n  templateUrl: './league-table-view.component.html',\n  styleUrls: ['./league-table-view.component.scss']\n})\nexport class LeagueTableViewComponent implements OnInit, AfterViewInit, OnDestroy {\n  @ViewChild('rowActionBtn') rowActionBtn: TemplateRef<any>;\n  @ViewChild('dateRangePicker', { static: false }) dateRangePicker: any;\n  @ViewChild(DataTableDirective, { static: false })\n  dtElement: any = DataTableDirective;\n  dtTrigger: Subject<ADTSettings> = new Subject<ADTSettings>();\n  dtOptions: any = {};\n\n  // Filter properties\n  public seasonId: any;\n  public clubId: any = null;\n  public tournamentId: any = null;\n  public dateRange: any = { start_date: null, end_date: null };\n  public dateRangeValue: string = '';\n  hoveredDate: NgbDate | null = null;\n  fromDate: NgbDate | null = null;\n  toDate: NgbDate | null = null;\n  isSelecting: boolean = false;\n  private clickOutsideTimeout: any;\n  public matchStatus: string = 'all';\n  public seasons: any[];\n  public clubs: any[];\n  public tournaments: any[];\n\n  public isTableLoading: boolean = false;\n  public table_name = 'team-table';\n\n  matchStatusOptions = [\n    { label: 'All Status', value: 'all' },\n    { label: 'Upcoming', value: 'upcoming' },\n    { label: 'Active', value: 'active' },\n    { label: 'Completed', value: 'completed' },\n    { label: 'Cancelled', value: 'cancelled' },\n  ];\n\n  public params: EditorSidebarParams = {\n    editor_id: this.table_name,\n    title: {\n      create: this._translateService.instant('Create New Tournament'),\n      edit: 'Edit team',\n      remove: 'Delete team'\n    },\n    url: `${environment.apiUrl}/teams/editor`,\n    method: 'POST',\n    action: 'create'\n  };\n\n  public fields: any[] = [\n    {\n      key: 'home_score',\n      type: 'number',\n      props: {\n        label: this._translateService.instant('Home team score'),\n        placeholder: this._translateService.instant('Enter score of team'),\n        required: true,\n      }\n    },\n    {\n      key: 'away_score',\n      type: 'number',\n      props: {\n        label: this._translateService.instant('Away team score'),\n        placeholder: this._translateService.instant('Enter score of team'),\n        required: true,\n      }\n    }\n  ];\n\n  constructor(\n    public _http: HttpClient,\n    public _commonsService: CommonsService,\n    public _translateService: TranslateService,\n    public _coreSidebarService: CoreSidebarService,\n    private _exportService: ExportService,\n    public _loadingService: LoadingService,\n    public _registrationService: RegistrationService,\n    public _clubService: ClubService,\n    private calendar: NgbCalendar,\n    public formatter: NgbDateParserFormatter\n  ) {}\n\n  ngOnInit(): void {\n    this._getCurrentSeason();\n    this._getClubs();\n    this.buildDataTable();\n  }\n\n  ngAfterViewInit(): void {\n    setTimeout(() => {\n      this.dtTrigger.next(this.dtOptions);\n    }, 500);\n  }\n\n  buildDataTable(): void {\n    this.dtOptions = {\n      dom: this._commonsService.dataTableDefaults.dom,\n      select: 'single',\n      rowId: 'id',\n      processing: true,\n      language: {\n        ...this._commonsService.dataTableDefaults.lang,\n        processing: ``,\n      },\n      ajax: (dataTablesParameters: any, callback) => {\n        // Build query parameters for all filters\n        let params = new URLSearchParams();\n\n        if (this.clubId != undefined && this.clubId !== null) {\n          params.append('club_id', this.clubId);\n        }\n\n        if (this.tournamentId != undefined && this.tournamentId !== null) {\n          params.append('tournament_id', this.tournamentId);\n        }\n\n        if (this.dateRange?.start_date) {\n          params.append('start_date', this.dateRange.start_date);\n        }\n\n        if (this.dateRange?.end_date) {\n          params.append('end_date', this.dateRange.end_date);\n        }\n\n        if (this.matchStatus && this.matchStatus !== 'all') {\n          params.append('match_status', this.matchStatus);\n        }\n\n        const queryString = params.toString();\n        const url = `${environment.apiUrl}/seasons/${this.seasonId}/matches${\n          queryString ? '?' + queryString : ''\n        }`;\n\n        this.isTableLoading = true;\n        this._http.post<any>(url, dataTablesParameters).subscribe(\n          (resp: any) => {\n            this.isTableLoading = false;\n            callback({\n              recordsTotal: resp.recordsTotal,\n              recordsFiltered: resp.recordsFiltered,\n              data: resp.data,\n            });\n          },\n          (error) => {\n            this.isTableLoading = false;\n            console.error('Error loading table data:', error);\n            callback({\n              recordsTotal: 0,\n              recordsFiltered: 0,\n              data: [],\n            });\n          }\n        );\n      },\n      responsive: false,\n      scrollX: true,\n      columnDefs: [{ responsivePriority: 1, targets: -1 }],\n      columns: [\n        {\n          title: this._translateService.instant('No.'),\n          data: null,\n          className: 'font-weight-bolder',\n          type: 'any-number',\n          render: function (data, type, row, metadata) {\n            return metadata.row + 1\n          }\n        },\n        {\n          title: this._translateService.instant('Tournament'),\n          data: 'name',\n          className: 'font-weight-bolder',\n          type: 'any-number',\n          render: {\n            display: (data, type, row) => {\n              return data ?? 'TBD';\n            },\n            filter: (data, type, row) => {\n              return data;\n            }\n          }\n        },\n        {\n          title: this._translateService.instant('Round'),\n          data: 'round_name',\n          render: function (data) {\n            const displayData = data || 'TBD';\n            return `<div class=\"text-center\" style=\"width:max-content;\">${displayData}</div>`;\n          },\n        },\n        {\n          title: this._translateService.instant('Date'),\n          data: 'start_time',\n          className: 'text-center',\n          render: function (data, type, row) {\n            const displayDate =\n              !data || data === 'TBD'\n                ? 'TBD'\n                : moment(data).format('YYYY-MM-DD');\n            return `<div class=\"text-center\" style=\"min-width: max-content;\">${displayDate}</div>`;\n          },\n        },\n        {\n          title: this._translateService.instant('Start time'),\n          data: 'start_time',\n          className: 'text-center',\n          render: function(data, type, row) {\n            if (!data || data == 'TBD') {\n              return 'TBD';\n            }\n            return moment(data).format('HH:mm');\n          }\n        },\n        {\n          title: this._translateService.instant('End time'),\n          data: 'end_time',\n          className: 'text-center',\n          render: function(data, type, row) {\n            if (!data || data == 'TBD') {\n              return 'TBD';\n            }\n            return moment(data).format('HH:mm');\n          }\n        },\n        {\n          title: this._translateService.instant('Location'),\n          data: 'location',\n          render: function(data, type, row) {\n            if (!data || data == 'TBD') {\n              return 'TBD';\n            }\n            return data;\n          }\n        },\n        {\n          title: this._translateService.instant('Home Team'),\n          data: 'home_team_name',\n          className: 'text-center'\n        },\n        {\n          title: 'VS',\n          data: null,\n          className: 'text-center',\n          render: function(data, type, row) {\n            return 'vs';\n          }\n        },\n        {\n          title: this._translateService.instant('Away Team'),\n          data: 'away_team_name',\n          className: 'text-center'\n        },\n        {\n          title: this._translateService.instant('Home score'),\n          data: 'home_score',\n          className: 'text-center'\n        },\n        {\n          data: null,\n          className: 'text-center',\n          render: function(data, type, row) {\n            return '-';\n          }\n        },\n        {\n          title: this._translateService.instant('Away score'),\n          data: 'away_score',\n          className: 'text-center'\n        }\n      ],\n      buttons: {\n        dom: this._commonsService.dataTableDefaults.buttons.dom,\n        buttons: [\n          {\n            text: `<i class=\"fa-solid fa-file-csv mr-1\"></i> ${this._translateService.instant(\n              'Export CSV'\n            )}`,\n            extend: 'csv',\n            action: async (e: any, dt: any, button: any, config: any) => {\n              const data = dt.buttons.exportData();\n              \n              // Generate filename with current filter information\n              let filename = 'Matches_Report';\n              \n              if (this.seasonId) {\n                const season = this.seasons?.find(s => s.id == this.seasonId);\n                if (season) {\n                  filename += `_${season.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              \n              if (this.tournamentId) {\n                const tournament = this.tournaments?.find(t => t.id == this.tournamentId);\n                if (tournament) {\n                  filename += `_${tournament.name.replace(/\\s+/g, '_')}`;\n                }\n              }\n              \n              if (this.clubId) {\n                const club = this.clubs?.find(c => c.id == this.clubId);\n                if (club) {\n                  filename += `_${club.code.replace(/\\s+/g, '_')}`;\n                }\n              }\n              \n              if (this.matchStatus && this.matchStatus !== 'all') {\n                filename += `_${this.matchStatus}`;\n              }\n              \n              if (this.dateRange?.start_date && this.dateRange?.end_date) {\n                filename += `_${this.dateRange.start_date}_to_${this.dateRange.end_date}`;\n              } else if (this.dateRange?.start_date) {\n                filename += `_from_${this.dateRange.start_date}`;\n              }\n              \n              filename += '.csv';\n              \n              await this._exportService.exportCsv(data, filename);\n            }\n          }\n        ]\n      }\n    };\n  }\n\n  refreshDataTable() {\n    if (this.dtElement?.dtInstance) {\n      this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {\n        dtInstance.ajax.reload();\n      });\n    }\n  }\n\n  // Filter methods\n  _getCurrentSeason() {\n    this._loadingService.show();\n    this._registrationService.getAllSeasonActive().subscribe(\n      (data) => {\n        this.seasons = data;\n        this.seasonId = this.seasons[0].id;\n        this._getTournaments();\n      },\n      (error) => {\n        Swal.fire({\n          title: 'Error',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      },\n      () => {\n        this._loadingService.dismiss();\n      }\n    );\n  }\n\n  _getClubs() {\n    this._clubService.getAllClubs().subscribe(\n      (res) => {\n        this.clubs = res.data;\n      },\n      (error) => {\n        Swal.fire({\n          title: 'Error',\n          text: error.message,\n          icon: 'error',\n          confirmButtonText: this._translateService.instant('OK')\n        });\n      }\n    );\n  }\n\n  _getTournaments() {\n    if (this.seasonId) {\n      this._http\n        .get<any>(\n          `${environment.apiUrl}/seasons/${this.seasonId}/tournaments?is_released=true`\n        )\n        .subscribe(\n          (data) => {\n            this.tournaments = data.data;\n          },\n          (error) => {\n            Swal.fire({\n              title: 'Error',\n              text: error.message,\n              icon: 'error',\n              confirmButtonText: this._translateService.instant('OK'),\n            });\n          }\n        );\n    }\n  }\n\n  onSelectSeason($event: any) {\n    return new Promise((resolve, reject) => {\n      this.seasonId = $event;\n      this._getTournaments();\n      this.tournamentId = null;\n      this.refreshDataTable();\n      resolve(true);\n    });\n  }\n\n  onSelectClub($event: any) {\n    this.clubId = $event;\n    this.refreshDataTable();\n  }\n\n  onSelectTournament($event: any) {\n    this.tournamentId = $event;\n    this.refreshDataTable();\n  }\n\n  onSelectMatchStatus($event: any) {\n    this.matchStatus = $event;\n    this.refreshDataTable();\n  }\n\n  // Date picker methods\n  onDateSelection(date: NgbDate) {\n    if (!this.fromDate && !this.toDate) {\n      this.fromDate = date;\n      this.isSelecting = true;\n    } else if (\n      this.fromDate &&\n      !this.toDate &&\n      date &&\n      date.after(this.fromDate)\n    ) {\n      this.toDate = date;\n      this.isSelecting = false;\n      this.updateDateRange();\n      setTimeout(() => {\n        if (this.dateRangePicker && this.dateRangePicker.close) {\n          this.dateRangePicker.close();\n        }\n      }, 0);\n    } else {\n      this.toDate = null;\n      this.fromDate = date;\n      this.isSelecting = true;\n    }\n  }\n\n  openDatePicker() {\n    this.isSelecting = false;\n    if (this.dateRangePicker) {\n      this.dateRangePicker.open();\n    }\n  }\n\n  @HostListener('document:click', ['$event'])\n  onDocumentClick(event: Event) {\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n\n    this.clickOutsideTimeout = setTimeout(() => {\n      this.handleClickOutside(event);\n    }, 50);\n  }\n\n  private handleClickOutside(event: Event) {\n    if (\n      !this.dateRangePicker ||\n      !this.dateRangePicker.isOpen ||\n      !this.dateRangePicker.isOpen()\n    ) {\n      return;\n    }\n\n    if (this.isSelecting) {\n      return;\n    }\n\n    const target = event.target as HTMLElement;\n    if (!target) return;\n\n    if (\n      target.closest('.btn') ||\n      target.classList.contains('feather') ||\n      target.classList.contains('icon-calendar')\n    ) {\n      return;\n    }\n\n    if (\n      target.tagName === 'INPUT' &&\n      target.getAttribute('name') === 'daterange'\n    ) {\n      return;\n    }\n\n    const datepickerElement = document.querySelector('ngb-datepicker');\n    if (datepickerElement && datepickerElement.contains(target)) {\n      return;\n    }\n\n    this.dateRangePicker.close();\n  }\n\n  clearDateRange() {\n    this.fromDate = null;\n    this.toDate = null;\n    this.isSelecting = false;\n    this.updateDateRange();\n  }\n\n  validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {\n    const parsed = this.formatter.parse(input);\n    return parsed && this.calendar.isValid(NgbDate.from(parsed))\n      ? NgbDate.from(parsed)\n      : currentValue;\n  }\n\n  isHovered(date: NgbDate) {\n    return (\n      this.fromDate &&\n      !this.toDate &&\n      this.hoveredDate &&\n      date.after(this.fromDate) &&\n      date.before(this.hoveredDate)\n    );\n  }\n\n  isInside(date: NgbDate) {\n    return this.toDate && date.after(this.fromDate) && date.before(this.toDate);\n  }\n\n  isRange(date: NgbDate) {\n    return (\n      date.equals(this.fromDate) ||\n      (this.toDate && date.equals(this.toDate)) ||\n      this.isInside(date) ||\n      this.isHovered(date)\n    );\n  }\n\n  onDateRangeChange() {\n    this.refreshDataTable();\n  }\n\n  updateDateRange() {\n    if (this.fromDate && this.toDate) {\n      const fromMoment = moment(\n        `${this.fromDate.year}-${this.fromDate.month\n          .toString()\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\n      );\n      const toMoment = moment(\n        `${this.toDate.year}-${this.toDate.month\n          .toString()\n          .padStart(2, '0')}-${this.toDate.day.toString().padStart(2, '0')}`\n      );\n\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = toMoment.format('YYYY-MM-DD');\n      this.dateRangeValue = `${fromMoment.format(\n        'MMM DD, YYYY'\n      )} - ${toMoment.format('MMM DD, YYYY')}`;\n\n      this.onDateRangeChange();\n    } else if (this.fromDate) {\n      const fromMoment = moment(\n        `${this.fromDate.year}-${this.fromDate.month\n          .toString()\n          .padStart(2, '0')}-${this.fromDate.day.toString().padStart(2, '0')}`\n      );\n      this.dateRange.start_date = fromMoment.format('YYYY-MM-DD');\n      this.dateRange.end_date = null;\n      this.dateRangeValue = fromMoment.format('MMM DD, YYYY');\n    } else {\n      this.dateRange.start_date = null;\n      this.dateRange.end_date = null;\n      this.dateRangeValue = '';\n      this.onDateRangeChange();\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.dtTrigger.unsubscribe();\n    if (this.clickOutsideTimeout) {\n      clearTimeout(this.clickOutsideTimeout);\n    }\n  }\n}\n", "<div class=\"p-1\">\n  <!-- Filters Section -->\n  <div class=\"row mb-1\">\n    <div class=\"col-12\">\n      <label for=\"season\" class=\"form-label\">{{'Season'|translate}}</label>\n      <ng-select\n        [searchable]=\"true\"\n        [clearable]=\"false\"\n        placeholder=\"{{'Select Season'|translate}}\"\n        [(ngModel)]=\"seasonId\"\n        (change)=\"onSelectSeason($event)\">\n        <ng-option *ngFor=\"let season of seasons\" [value]=\"season.id\">\n          {{ season.name | translate }}\n        </ng-option>\n      </ng-select>\n    </div>\n  </div>\n  <div class=\"row mb-1\">\n    <div class=\"col-md-3\">\n      <label for=\"tournament\" class=\"form-label\">{{'Tournament'|translate}}</label>\n      <ng-select\n        [searchable]=\"true\"\n        [clearable]=\"true\"\n        placeholder=\"{{'Select Tournament'|translate}}\"\n        [(ngModel)]=\"tournamentId\"\n        (change)=\"onSelectTournament($event)\">\n        <ng-option *ngFor=\"let tournament of tournaments\" [value]=\"tournament.id\">\n          {{ tournament.name | translate }}\n        </ng-option>\n      </ng-select>\n    </div>\n    <div class=\"col-md-3\">\n      <label for=\"club\" class=\"form-label\">{{'Club'|translate}}</label>\n      <ng-select\n        [searchable]=\"true\"\n        [clearable]=\"true\"\n        placeholder=\"{{'Select Club'|translate}}\"\n        [(ngModel)]=\"clubId\"\n        (change)=\"onSelectClub($event)\">\n        <ng-option *ngFor=\"let club of clubs\" [value]=\"club.id\">\n          {{ club.code | translate }}\n        </ng-option>\n      </ng-select>\n    </div>\n    <div class=\"col-md-3\">\n      <label for=\"dateRange\" class=\"form-label\">{{'Date Range'|translate}}</label>\n      <div class=\"input-group\">\n        <input\n          name=\"daterange\"\n          class=\"form-control\"\n          placeholder=\"{{'Select Date Range'|translate}}\"\n          ngbDatepicker\n          readonly\n          #dateRangePicker=\"ngbDatepicker\"\n          [value]=\"formatter.format(fromDate) + (toDate ? ' - ' + formatter.format(toDate) : '')\"\n          (click)=\"openDatePicker()\"\n          [dayTemplate]=\"dayTemplate\"\n          [footerTemplate]=\"footerTemplate\"\n          (dateSelect)=\"onDateSelection($event)\"\n          [firstDayOfWeek]=\"1\"\n          [displayMonths]=\"2\"\n          outsideDays=\"hidden\"\n          [autoClose]=\"false\"\n        />\n        <div class=\"input-group-append\">\n          <button\n            class=\"btn btn-outline-secondary\"\n            (click)=\"openDatePicker()\"\n            type=\"button\">\n            <i class=\"feather icon-calendar\"></i>\n          </button>\n        </div>\n      </div>\n\n      <ng-template #dayTemplate let-date=\"date\" let-focused=\"focused\">\n        <span\n          class=\"custom-day\"\n          [class.focused]=\"focused\"\n          [class.range]=\"isRange(date)\"\n          [class.faded]=\"isHovered(date) || isInside(date)\"\n          (mouseenter)=\"hoveredDate = date\"\n          (mouseleave)=\"hoveredDate = null\"\n        >\n          {{ date.day }}\n        </span>\n      </ng-template>\n\n      <ng-template #footerTemplate>\n        <hr class=\"my-0\">\n        <div class=\"d-flex justify-content-between p-2\">\n          <button\n            type=\"button\"\n            class=\"btn btn-outline-secondary btn-sm\"\n            (click)=\"clearDateRange(); dateRangePicker.close()\"\n          >\n            Clear\n          </button>\n          <button\n            type=\"button\"\n            class=\"btn btn-primary btn-sm\"\n            (click)=\"dateRangePicker.close()\"\n          >\n            Close\n          </button>\n        </div>\n      </ng-template>\n    </div>\n    <div class=\"col-md-3\">\n      <label for=\"matchStatus\" class=\"form-label\">{{'Match Status'|translate}}</label>\n      <ng-select\n        [searchable]=\"false\"\n        [clearable]=\"false\"\n        placeholder=\"{{'Select Status'|translate}}\"\n        [(ngModel)]=\"matchStatus\"\n        (change)=\"onSelectMatchStatus($event)\">\n        <ng-option *ngFor=\"let status of matchStatusOptions\" [value]=\"status.value\">\n          {{ status.label | translate }}\n        </ng-option>\n      </ng-select>\n    </div>\n  </div>\n\n  <!-- Data Table Section -->\n  <div class=\"row mb-1\">\n    <div class=\"col-12\">\n      <table datatable [dtOptions]=\"dtOptions\" [dtTrigger]=\"dtTrigger\" class=\"table border row-border hover\">\n      </table>\n    </div>\n  </div>\n</div>\n\n<core-sidebar class=\"modal modal-slide-in sidebar-todo-modal fade\" [name]=\"table_name\" overlayClass=\"modal-backdrop\">\n  <app-editor-sidebar [table]=\"dtElement\" [fields]=\"fields\" [params]=\"params\">\n  </app-editor-sidebar>\n</core-sidebar>\n\n<ng-template #rowActionBtn let-data=\"adtData\" let-emitter=\"captureEvents\">\n  <app-btn-dropdown-action [actions]=\"rowActions\" [data]=\"data\" (emitter)=\"emitter($event)\"\n    btnStyle=\"font-size:15px;color:black!important\"></app-btn-dropdown-action>\n</ng-template>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}