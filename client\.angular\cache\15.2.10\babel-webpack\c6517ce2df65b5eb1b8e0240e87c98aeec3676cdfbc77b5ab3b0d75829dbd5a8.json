{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@angular/platform-browser\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"app/layout/components/content-header/content-header.component\";\nimport * as i5 from \"./league-table-view/league-table-view.component\";\nimport * as i6 from \"./schedule-view/schedule-view.component\";\nfunction LeagueReportsComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-league-table-view\");\n  }\n}\nfunction LeagueReportsComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-schedule-view\");\n  }\n}\nexport class LeagueReportsComponent {\n  constructor(_trans, _titleService) {\n    this._trans = _trans;\n    this._titleService = _titleService;\n    this._titleService.setTitle('Matches Report');\n  }\n  ngOnInit() {\n    this.contentHeader = {\n      headerTitle: this._trans.instant('Matches Report'),\n      actionButton: false,\n      breadcrumb: {\n        type: '',\n        links: [{\n          name: this._trans.instant('Home'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Tournaments'),\n          isLink: false\n        }, {\n          name: this._trans.instant('Matches Report'),\n          isLink: false\n        }]\n      }\n    };\n  }\n  static #_ = this.ɵfac = function LeagueReportsComponent_Factory(t) {\n    return new (t || LeagueReportsComponent)(i0.ɵɵdirectiveInject(i1.TranslateService), i0.ɵɵdirectiveInject(i2.Title));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LeagueReportsComponent,\n    selectors: [[\"app-league-reports\"]],\n    decls: 22,\n    vars: 8,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [\"id\", \"league-reports-page\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", \"m-0\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"league_table\"], [\"ngbNavLink\", \"\"], [1, \"fa-light\", \"fa-table-list\", \"mr-1\"], [\"ngbNavContent\", \"\"], [\"ngbNavItem\", \"schedule_matches\"], [1, \"fa-light\", \"fa-calendar\", \"mr-1\"], [1, \"mt-2\", 3, \"ngbNavOutlet\"]],\n    template: function LeagueReportsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"section\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"ul\", 7, 8)(9, \"li\", 9)(10, \"a\", 10);\n        i0.ɵɵelement(11, \"i\", 11);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(14, LeagueReportsComponent_ng_template_14_Template, 1, 0, \"ng-template\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"li\", 13)(16, \"a\", 10);\n        i0.ɵɵelement(17, \"i\", 14);\n        i0.ɵɵtext(18);\n        i0.ɵɵpipe(19, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(20, LeagueReportsComponent_ng_template_20_Template, 1, 0, \"ng-template\", 12);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(21, \"div\", 15);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(8);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(10);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 4, \"Table View\"), \" \");\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 6, \"Schedule View\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngbNavOutlet\", _r0);\n      }\n    },\n    dependencies: [i3.NgbNavContent, i3.NgbNav, i3.NgbNavItem, i3.NgbNavLink, i3.NgbNavOutlet, i4.ContentHeaderComponent, i5.LeagueTableViewComponent, i6.ScheduleViewComponent, i1.TranslatePipe],\n    styles: [\".min-w-max[_ngcontent-%COMP%] {\\n  min-width: max-content;\\n}\\n\\n.custom-day[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 0.185rem 0.25rem;\\n  border-radius: 0.25rem;\\n  display: inline-block;\\n  width: 2rem;\\n  cursor: pointer;\\n}\\n\\n.custom-day[_ngcontent-%COMP%]:hover, .custom-day.focused[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n}\\n\\n.custom-day.range[_ngcontent-%COMP%], .custom-day[_ngcontent-%COMP%]:hover {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.custom-day.faded[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 123, 255, 0.5);\\n  color: white;\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  background: transparent;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvbGVhZ3VlLXRvdXJuYW1lbnQvbGVhZ3VlLXJlcG9ydHMvbGVhZ3VlLXJlcG9ydHMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxzQkFBQTtBQUNGOztBQUdBO0VBQ0Usa0JBQUE7RUFDQSx5QkFBQTtFQUNBLHNCQUFBO0VBQ0EscUJBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQTtBQUFGOztBQUdBOztFQUVFLHlCQUFBO0FBQUY7O0FBR0E7O0VBRUUseUJBQUE7RUFDQSxZQUFBO0FBQUY7O0FBR0E7RUFDRSx3Q0FBQTtFQUNBLFlBQUE7QUFBRjs7QUFHQTtFQUNFLHVCQUFBO0FBQUYiLCJzb3VyY2VzQ29udGVudCI6WyIubWluLXctbWF4IHtcclxuICBtaW4td2lkdGg6IG1heC1jb250ZW50O1xyXG59XHJcblxyXG4vLyBEYXRlIHJhbmdlIHBpY2tlciBzdHlsZXNcclxuLmN1c3RvbS1kYXkge1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBwYWRkaW5nOiAwLjE4NXJlbSAwLjI1cmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XHJcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gIHdpZHRoOiAycmVtO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuLmN1c3RvbS1kYXk6aG92ZXIsXHJcbi5jdXN0b20tZGF5LmZvY3VzZWQge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNlOWVjZWY7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF5LnJhbmdlLFxyXG4uY3VzdG9tLWRheTpob3ZlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzAwN2JmZjtcclxuICBjb2xvcjogd2hpdGU7XHJcbn1cclxuXHJcbi5jdXN0b20tZGF5LmZhZGVkIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDEyMywgMjU1LCAwLjUpO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxufVxyXG5cclxuLm5hdi1saW5rIHtcclxuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "mappings": ";;;;;;;;;IAiBkBA,EAAA,CAAAC,SAAA,4BAA+C;;;;;IAS/CD,EAAA,CAAAC,SAAA,wBAAuC;;;ACdzD,OAAM,MAAOC,sBAAsB;EAIjCC,YACSC,MAAwB,EACxBC,aAAoB;IADpB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAEpB,IAAI,CAACA,aAAa,CAACC,QAAQ,CAAC,gBAAgB,CAAC;EAC/C;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG;MACnBC,WAAW,EAAE,IAAI,CAACL,MAAM,CAACM,OAAO,CAAC,gBAAgB,CAAC;MAClDC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,IAAI,CAACX,MAAM,CAACM,OAAO,CAAC,MAAM,CAAC;UACjCM,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACX,MAAM,CAACM,OAAO,CAAC,aAAa,CAAC;UACxCM,MAAM,EAAE;SACT,EACD;UACED,IAAI,EAAE,IAAI,CAACX,MAAM,CAACM,OAAO,CAAC,gBAAgB,CAAC;UAC3CM,MAAM,EAAE;SACT;;KAGN;EACH;EAAC,QAAAC,CAAA;qBAjCUf,sBAAsB,EAAAF,EAAA,CAAAkB,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAApB,EAAA,CAAAkB,iBAAA,CAAAG,EAAA,CAAAC,KAAA;EAAA;EAAA,QAAAC,EAAA;UAAtBrB,sBAAsB;IAAAsB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QDZnC9B,EAAA,CAAAgC,cAAA,aAA+C;QAG3ChC,EAAA,CAAAC,SAAA,4BAAyE;QAEzED,EAAA,CAAAgC,cAAA,iBAAkC;QAQpBhC,EAAA,CAAAC,SAAA,aAA2C;QAC3CD,EAAA,CAAAiC,MAAA,IACF;;QAAAjC,EAAA,CAAAkC,YAAA,EAAI;QACJlC,EAAA,CAAAmC,UAAA,KAAAC,8CAAA,0BAEc;QAChBpC,EAAA,CAAAkC,YAAA,EAAK;QACLlC,EAAA,CAAAgC,cAAA,cAAkC;QAE9BhC,EAAA,CAAAC,SAAA,aAAyC;QACzCD,EAAA,CAAAiC,MAAA,IACF;;QAAAjC,EAAA,CAAAkC,YAAA,EAAI;QACJlC,EAAA,CAAAmC,UAAA,KAAAE,8CAAA,0BAEc;QAChBrC,EAAA,CAAAkC,YAAA,EAAK;QAGTlC,EAAA,CAAAC,SAAA,eAA6C;QAC/CD,EAAA,CAAAkC,YAAA,EAAM;;;;QA7BUlC,EAAA,CAAAsC,SAAA,GAA+B;QAA/BtC,EAAA,CAAAuC,UAAA,kBAAAR,GAAA,CAAAvB,aAAA,CAA+B;QAWrCR,EAAA,CAAAsC,SAAA,IACF;QADEtC,EAAA,CAAAwC,kBAAA,MAAAxC,EAAA,CAAAyC,WAAA,2BACF;QAQEzC,EAAA,CAAAsC,SAAA,GACF;QADEtC,EAAA,CAAAwC,kBAAA,MAAAxC,EAAA,CAAAyC,WAAA,8BACF;QAODzC,EAAA,CAAAsC,SAAA,GAAoB;QAApBtC,EAAA,CAAAuC,UAAA,iBAAAG,GAAA,CAAoB", "names": ["i0", "ɵɵelement", "LeagueReportsComponent", "constructor", "_trans", "_titleService", "setTitle", "ngOnInit", "contentHeader", "headerTitle", "instant", "actionButton", "breadcrumb", "type", "links", "name", "isLink", "_", "ɵɵdirectiveInject", "i1", "TranslateService", "i2", "Title", "_2", "selectors", "decls", "vars", "consts", "template", "LeagueReportsComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "LeagueReportsComponent_ng_template_14_Template", "LeagueReportsComponent_ng_template_20_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "_r0"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.html", "D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\league-reports.component.ts"], "sourcesContent": ["<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n\r\n    <section id=\"league-reports-page\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <!-- Tabs Section -->\r\n          <div class=\"card\">\r\n            <ul ngbNav #nav=\"ngbNav\" class=\"nav-tabs m-0\">\r\n              <li ngbNavItem=\"league_table\">\r\n                <a ngbNavLink>\r\n                  <i class=\"fa-light fa-table-list mr-1\"></i>\r\n                  {{ 'Table View' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <app-league-table-view></app-league-table-view>\r\n                </ng-template>\r\n              </li>\r\n              <li ngbNavItem=\"schedule_matches\">\r\n                <a ngbNavLink>\r\n                  <i class=\"fa-light fa-calendar mr-1\"></i>\r\n                  {{ 'Schedule View' | translate }}\r\n                </a>\r\n                <ng-template ngbNavContent>\r\n                  <app-schedule-view></app-schedule-view>\r\n                </ng-template>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n          <div [ngbNavOutlet]=\"nav\" class=\"mt-2\"></div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</div>\r\n\r\n", "import {\r\n  Component,\r\n  OnInit\r\n} from '@angular/core';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { Title } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'app-league-reports',\r\n  templateUrl: './league-reports.component.html',\r\n  styleUrls: ['./league-reports.component.scss'],\r\n})\r\nexport class LeagueReportsComponent implements OnInit\r\n{\r\n  public contentHeader: object;\r\n\r\n  constructor(\r\n    public _trans: TranslateService,\r\n    public _titleService: Title\r\n  ) {\r\n    this._titleService.setTitle('Matches Report');\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.contentHeader = {\r\n      headerTitle: this._trans.instant('Matches Report'),\r\n      actionButton: false,\r\n      breadcrumb: {\r\n        type: '',\r\n        links: [\r\n          {\r\n            name: this._trans.instant('Home'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Tournaments'),\r\n            isLink: false\r\n          },\r\n          {\r\n            name: this._trans.instant('Matches Report'),\r\n            isLink: false\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}