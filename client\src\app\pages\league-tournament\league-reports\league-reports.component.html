<div class="content-wrapper container-xxl p-0">
  <div class="content-body">
    <!-- content-header component -->
    <app-content-header [contentHeader]="contentHeader"></app-content-header>

    <section id="league-reports-page">
      <div class="row">
        <div class="col-12">
          <!-- Tabs Section -->
          <div class="card">
            <ul ngbNav #nav="ngbNav" class="nav-tabs m-0">
              <li ngbNavItem="league_table">
                <a ngbNavLink>
                  <i class="fa-light fa-table-list mr-1"></i>
                  {{ 'Table View' | translate }}
                </a>
                <ng-template ngbNavContent>
                  <app-league-table-view></app-league-table-view>
                </ng-template>
              </li>
              <li ngbNavItem="schedule_matches">
                <a ngbNavLink>
                  <i class="fa-light fa-calendar mr-1"></i>
                  {{ 'Schedule View' | translate }}
                </a>
                <ng-template ngbNavContent>
                  <app-schedule-view></app-schedule-view>
                </ng-template>
              </li>
            </ul>
          </div>
          <div [ngbNavOutlet]="nav" class="mt-2"></div>
        </div>
      </div>
    </section>
  </div>
</div>

