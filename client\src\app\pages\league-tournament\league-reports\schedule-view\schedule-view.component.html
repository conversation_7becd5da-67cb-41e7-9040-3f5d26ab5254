<div class="p-1" *ngIf="hasSeason">
  <div class="row mb-1">
    <div class="col-12">
      <label for="season" class="form-label">{{'Season'|translate}}</label>
      <ng-select
        [searchable]="true"
        [clearable]="false"
        placeholder="{{'Select Season'|translate}}"
        [(ngModel)]="params.season_id"
        (change)="getTournamentOptions($event)">
        <ng-option *ngFor="let season of seasons" [value]="season.id">
          {{ season.name | translate }}
        </ng-option>
      </ng-select>
    </div>
  </div>

  <div class="row mb-1">
    <div class="col-12">
      <label for="tournament" class="form-label">{{'Tournament'|translate}}</label>
      <ng-select
        [searchable]="true"
        [clearable]="false"
        placeholder="{{'Select Tournament'|translate}}"
        [(ngModel)]="params.tournament_id"
        (change)="onTournamentChange($event)">
        <ng-option *ngFor="let tournament of tournaments" [value]="tournament.id">
          {{ tournament.name | translate }}
        </ng-option>
      </ng-select>
    </div>
  </div>

  <div class="mt-2">
    <scrollable-tabs [tabs]="tabs" (emitSelectedTab)="selectedTab($event)" [onSelectTab]="onSelectTab"></scrollable-tabs>
  </div>

  <div class="collapse-icon" style="margin-top: 2rem;">
    <ngb-accordion #acc="ngbAccordion" [activeIds]="activeIds" [destroyOnHide]="false">
      <ngb-panel id="match-panel-{{ id }}"
                 *ngFor="let matches_date of matches | keyvalue:_commonsService.originalOrder; let id = index">
        <ng-template ngbPanelTitle>
          <span class="lead collapse-title" id="{{ (matches_date.key | date : 'YYYY-MM-dd') }}">
            {{ matches_date.key ? (matches_date.key | date : 'YYYY-MM-dd') : 'TBD' }}
          </span>
        </ng-template>

        <ng-template ngbPanelContent>
          <table class="table table-fixtures">
            <tbody>
              <ng-container *ngFor="let type of matches_date.value | keyvalue:_commonsService.originalOrder">
                <ng-container *ngFor="let groups of type.value | keyvalue:_commonsService.originalOrder">
                  <tr *ngIf="type.key != AppConfig.TOURNAMENT_TYPES.league" class="tr-tile">
                    <td colspan="6" class="pl-0 pb-25">
                      <h5>{{ type.key | translate }} {{ 'Stage' | translate }}</h5>
                      <span>{{ groups.key }}</span>
                    </td>
                  </tr>
                  <ng-container *ngFor="let item of groups.value">
                    <tr row-match [match]="item" (click)="onSelectMatch(item)"></tr>
                  </ng-container>
                </ng-container>
              </ng-container>
            </tbody>
          </table>
        </ng-template>
      </ngb-panel>
    </ngb-accordion>
  </div>
</div>

<div *ngIf="!hasSeason">
  <div class="text-center">
    <h5 class="text-muted">{{ 'No matches found' | translate }}</h5>
  </div>
</div>
