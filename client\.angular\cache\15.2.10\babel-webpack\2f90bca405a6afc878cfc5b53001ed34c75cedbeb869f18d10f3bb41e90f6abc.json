{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter } from '@angular/core';\nimport moment from 'moment';\nimport { coreConfig } from '../../../../app-config';\nimport { AppConfig } from 'app/app-config';\nlet ScheduleViewComponent = class ScheduleViewComponent {\n  constructor(_http, _tourService, _seasonService, _loadingService, _commonsService, _translateService) {\n    this._http = _http;\n    this._tourService = _tourService;\n    this._seasonService = _seasonService;\n    this._loadingService = _loadingService;\n    this._commonsService = _commonsService;\n    this._translateService = _translateService;\n    this.onSelectTab = new EventEmitter();\n    this.activeIds = '';\n    this.selectedTabIndex = 0;\n    this.tabs = [];\n    this.matches = {};\n    this.seasons = [];\n    this.teams = [];\n    this.tournaments = [];\n    this.coreConfig = coreConfig;\n    this.params = {\n      season_id: null,\n      tournament_id: null\n    };\n    this.hasSeason = true;\n    this.AppConfig = AppConfig;\n    // Initialize with default values - don't use router to avoid conflicts\n    this.params.tournament_id = null;\n    this.params.season_id = null;\n  }\n  ngOnInit() {\n    this.getSeasons();\n  }\n  ngAfterViewInit() {\n    // Implementation for after view init\n  }\n  ngOnDestroy() {\n    // Cleanup if needed\n  }\n  getSeasons() {\n    this._seasonService.getCurrentSeason('matches').toPromise().then(res => {\n      this.seasons = res;\n      if (this.seasons.length == 0) {\n        this._loadingService.dismiss();\n        this.hasSeason = false;\n        return;\n      }\n      this.params.season_id = this.params.season_id ? this.params.season_id : res[0].id;\n      this.getTournamentOptions(this.params.season_id);\n    });\n  }\n  getTournamentOptions(season_id) {\n    this._seasonService.getTournamentOptions(season_id, 0, true).subscribe(res => {\n      this.tournaments = res.tournaments;\n      if (this.tournaments.length == 0) {\n        this.tabs = [];\n      }\n      this.params.tournament_id = this.params.tournament_id || res.tournaments[0]?.id || null;\n      this.selectedTournament = this.tournaments.find(tournament => tournament.id === this.params.tournament_id);\n      this.onSelectTournament(this.selectedTournament);\n    });\n  }\n  onSelectTournament(tournament) {\n    this.selectedTournament = tournament;\n    this.params.tournament_id = tournament?.id;\n    // Remove router navigation to avoid conflicts with Table View\n    this.getMatchesWithQuery();\n  }\n  getMatchesWithQuery() {\n    this._tourService.getMatchesWithQuery(this.params.season_id, `?tournament_id=${this.selectedTournament?.id}`).subscribe(res => {\n      let today = moment().format('YYYY-MM-DD');\n      this.matches = res.matches;\n      let numberOfMatches = Object.keys(this.matches).length;\n      let tabs = [];\n      if (numberOfMatches == 0) {\n        this.tabs = tabs;\n        return;\n      }\n      let count = 0;\n      this.activeIds = '';\n      for (let key in this.matches) {\n        if (key) {\n          this.activeIds += `match-panel-${count},`;\n        }\n        count++;\n        let index = tabs.length;\n        let label = 'TBD';\n        let date = 'TBD';\n        if (key) {\n          label = moment(key).format('ddd DD MMM');\n          date = moment(key).format('YYYY-MM-DD');\n        }\n        if (date == today) {\n          label = 'Today';\n        }\n        if (label != 'TBD') {\n          tabs.push({\n            label: label,\n            value: date,\n            index: index\n          });\n        }\n      }\n      if (this.matches) {\n        // select tab has date nearest to today\n        let nearestDate = Object.keys(this.matches).reduce((a, b) => {\n          return Math.abs(moment(a).diff(today, 'days')) < Math.abs(moment(b).diff(today, 'days')) ? a : b;\n        });\n        this.selectedTabIndex = tabs.findIndex(tab => tab.value === moment(nearestDate).format('YYYY-MM-DD'));\n        // sort tabs by date\n        tabs.sort((a, b) => {\n          return moment(a.value).diff(moment(b.value));\n        });\n        this.tabs = tabs;\n        setTimeout(() => {\n          this.onSelectTab.emit(this.selectedTabIndex);\n        }, 500);\n      }\n    }, error => {\n      console.error('Error loading matches:', error);\n    }, () => {\n      this._loadingService.dismiss();\n    });\n  }\n  selectedTab(tab) {\n    this.tab = tab;\n    let element = document.getElementById(tab.value);\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start'\n      });\n    }\n  }\n  onSelectMatch(match) {\n    // Handle match selection if needed\n    console.log('Selected match:', match);\n  }\n  onTournamentChange(tournamentId) {\n    const tournament = this.tournaments.find(t => t.id === tournamentId);\n    this.onSelectTournament(tournament);\n  }\n};\nScheduleViewComponent = __decorate([Component({\n  selector: 'app-schedule-view',\n  templateUrl: './schedule-view.component.html',\n  styleUrls: ['./schedule-view.component.scss']\n})], ScheduleViewComponent);\nexport { ScheduleViewComponent };", "map": {"version": 3, "mappings": ";AAAA,SACEA,SAAS,EACTC,YAAY,QAIP,eAAe;AAGtB,OAAOC,MAAM,MAAM,QAAQ;AAE3B,SAASC,UAAU,QAAQ,wBAAwB;AAEnD,SAASC,SAAS,QAAQ,gBAAgB;AAQnC,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAiBhCC,YACUC,KAAiB,EAClBC,YAA+B,EAC/BC,cAA6B,EAC7BC,eAA+B,EAC/BC,eAA+B,EAC/BC,iBAAmC;IALlC,KAAAL,KAAK,GAALA,KAAK;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAtB1B,KAAAC,WAAW,GAAG,IAAIZ,YAAY,EAAE;IAChC,KAAAa,SAAS,GAAG,EAAE;IACd,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,IAAI,GAAG,EAAE;IACT,KAAAC,OAAO,GAAQ,EAAE;IAIjB,KAAAC,OAAO,GAAG,EAAE;IACZ,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAjB,UAAU,GAAGA,UAAU;IACvB,KAAAkB,MAAM,GAAG;MAAEC,SAAS,EAAE,IAAI;MAAEC,aAAa,EAAE;IAAI,CAAE;IACjD,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAApB,SAAS,GAAGA,SAAS;IAUnB;IACA,IAAI,CAACiB,MAAM,CAACE,aAAa,GAAG,IAAI;IAChC,IAAI,CAACF,MAAM,CAACC,SAAS,GAAG,IAAI;EAC9B;EAEAG,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,eAAeA,CAAA;IACb;EAAA;EAGFC,WAAWA,CAAA;IACT;EAAA;EAGFF,UAAUA,CAAA;IACR,IAAI,CAACjB,cAAc,CAChBoB,gBAAgB,CAAC,SAAS,CAAC,CAC3BC,SAAS,EAAE,CACXC,IAAI,CAAEC,GAAG,IAAI;MACZ,IAAI,CAACd,OAAO,GAAGc,GAAG;MAClB,IAAI,IAAI,CAACd,OAAO,CAACe,MAAM,IAAI,CAAC,EAAE;QAC5B,IAAI,CAACvB,eAAe,CAACwB,OAAO,EAAE;QAC9B,IAAI,CAACV,SAAS,GAAG,KAAK;QACtB;;MAEF,IAAI,CAACH,MAAM,CAACC,SAAS,GAAG,IAAI,CAACD,MAAM,CAACC,SAAS,GACzC,IAAI,CAACD,MAAM,CAACC,SAAS,GACrBU,GAAG,CAAC,CAAC,CAAC,CAACG,EAAE;MACb,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACf,MAAM,CAACC,SAAS,CAAC;IAClD,CAAC,CAAC;EACN;EAEAc,oBAAoBA,CAACd,SAAc;IACjC,IAAI,CAACb,cAAc,CAChB2B,oBAAoB,CAACd,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,CACxCe,SAAS,CAAEL,GAAG,IAAI;MACjB,IAAI,CAACZ,WAAW,GAAGY,GAAG,CAACZ,WAAW;MAClC,IAAI,IAAI,CAACA,WAAW,CAACa,MAAM,IAAI,CAAC,EAAE;QAChC,IAAI,CAACjB,IAAI,GAAG,EAAE;;MAGhB,IAAI,CAACK,MAAM,CAACE,aAAa,GACvB,IAAI,CAACF,MAAM,CAACE,aAAa,IAAIS,GAAG,CAACZ,WAAW,CAAC,CAAC,CAAC,EAAEe,EAAE,IAAI,IAAI;MAC7D,IAAI,CAACG,kBAAkB,GAAG,IAAI,CAAClB,WAAW,CAACmB,IAAI,CAC5CC,UAAU,IAAKA,UAAU,CAACL,EAAE,KAAK,IAAI,CAACd,MAAM,CAACE,aAAa,CAC5D;MACD,IAAI,CAACkB,kBAAkB,CAAC,IAAI,CAACH,kBAAkB,CAAC;IAClD,CAAC,CAAC;EACN;EAEAG,kBAAkBA,CAACD,UAAe;IAChC,IAAI,CAACF,kBAAkB,GAAGE,UAAU;IACpC,IAAI,CAACnB,MAAM,CAACE,aAAa,GAAGiB,UAAU,EAAEL,EAAE;IAC1C;IACA,IAAI,CAACO,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,IAAI,CAAClC,YAAY,CACdkC,mBAAmB,CAClB,IAAI,CAACrB,MAAM,CAACC,SAAS,EACrB,kBAAkB,IAAI,CAACgB,kBAAkB,EAAEH,EAAE,EAAE,CAChD,CACAE,SAAS,CACPL,GAAG,IAAI;MACN,IAAIW,KAAK,GAAGzC,MAAM,EAAE,CAAC0C,MAAM,CAAC,YAAY,CAAC;MACzC,IAAI,CAAC3B,OAAO,GAAGe,GAAG,CAACf,OAAO;MAC1B,IAAI4B,eAAe,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC9B,OAAO,CAAC,CAACgB,MAAM;MACtD,IAAIjB,IAAI,GAAG,EAAE;MACb,IAAI6B,eAAe,IAAI,CAAC,EAAE;QACxB,IAAI,CAAC7B,IAAI,GAAGA,IAAI;QAChB;;MAEF,IAAIgC,KAAK,GAAG,CAAC;MACb,IAAI,CAAClC,SAAS,GAAG,EAAE;MACnB,KAAK,IAAImC,GAAG,IAAI,IAAI,CAAChC,OAAO,EAAE;QAC5B,IAAIgC,GAAG,EAAE;UACP,IAAI,CAACnC,SAAS,IAAI,eAAekC,KAAK,GAAG;;QAE3CA,KAAK,EAAE;QACP,IAAIE,KAAK,GAAGlC,IAAI,CAACiB,MAAM;QACvB,IAAIkB,KAAK,GAAG,KAAK;QACjB,IAAIC,IAAI,GAAG,KAAK;QAChB,IAAIH,GAAG,EAAE;UACPE,KAAK,GAAGjD,MAAM,CAAC+C,GAAG,CAAC,CAACL,MAAM,CAAC,YAAY,CAAC;UACxCQ,IAAI,GAAGlD,MAAM,CAAC+C,GAAG,CAAC,CAACL,MAAM,CAAC,YAAY,CAAC;;QAGzC,IAAIQ,IAAI,IAAIT,KAAK,EAAE;UACjBQ,KAAK,GAAG,OAAO;;QAEjB,IAAIA,KAAK,IAAI,KAAK,EAAE;UAClBnC,IAAI,CAACqC,IAAI,CAAC;YACRF,KAAK,EAAEA,KAAK;YACZG,KAAK,EAAEF,IAAI;YACXF,KAAK,EAAEA;WACR,CAAC;;;MAIN,IAAI,IAAI,CAACjC,OAAO,EAAE;QAChB;QACA,IAAIsC,WAAW,GAAGT,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC9B,OAAO,CAAC,CAACuC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UAC1D,OAAOC,IAAI,CAACC,GAAG,CAAC1D,MAAM,CAACuD,CAAC,CAAC,CAACI,IAAI,CAAClB,KAAK,EAAE,MAAM,CAAC,CAAC,GAC5CgB,IAAI,CAACC,GAAG,CAAC1D,MAAM,CAACwD,CAAC,CAAC,CAACG,IAAI,CAAClB,KAAK,EAAE,MAAM,CAAC,CAAC,GACrCc,CAAC,GACDC,CAAC;QACP,CAAC,CAAC;QAEF,IAAI,CAAC3C,gBAAgB,GAAGC,IAAI,CAAC8C,SAAS,CACnCC,GAAG,IAAKA,GAAG,CAACT,KAAK,KAAKpD,MAAM,CAACqD,WAAW,CAAC,CAACX,MAAM,CAAC,YAAY,CAAC,CAChE;QAED;QACA5B,IAAI,CAACgD,IAAI,CAAC,CAACP,CAAC,EAAEC,CAAC,KAAI;UACjB,OAAOxD,MAAM,CAACuD,CAAC,CAACH,KAAK,CAAC,CAACO,IAAI,CAAC3D,MAAM,CAACwD,CAAC,CAACJ,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC;QACF,IAAI,CAACtC,IAAI,GAAGA,IAAI;QAChBiD,UAAU,CAAC,MAAK;UACd,IAAI,CAACpD,WAAW,CAACqD,IAAI,CAAC,IAAI,CAACnD,gBAAgB,CAAC;QAC9C,CAAC,EAAE,GAAG,CAAC;;IAEX,CAAC,EACAoD,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,EACD,MAAK;MACH,IAAI,CAACzD,eAAe,CAACwB,OAAO,EAAE;IAChC,CAAC,CACF;EACL;EAEAmC,WAAWA,CAACN,GAAG;IACb,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAIO,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACT,GAAG,CAACT,KAAK,CAAC;IAChD,IAAIgB,OAAO,EAAE;MACXA,OAAO,CAACG,cAAc,CAAC;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAO,CAAE,CAAC;;EAElE;EAEAC,aAAaA,CAACC,KAAK;IACjB;IACAT,OAAO,CAACU,GAAG,CAAC,iBAAiB,EAAED,KAAK,CAAC;EACvC;EAIAE,kBAAkBA,CAACC,YAAiB;IAClC,MAAMxC,UAAU,GAAG,IAAI,CAACpB,WAAW,CAACmB,IAAI,CAAC0C,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAK6C,YAAY,CAAC;IACpE,IAAI,CAACvC,kBAAkB,CAACD,UAAU,CAAC;EACrC;CACD;AAnLYnC,qBAAqB,GAAA6E,UAAA,EALjClF,SAAS,CAAC;EACTmF,QAAQ,EAAE,mBAAmB;EAC7BC,WAAW,EAAE,gCAAgC;EAC7CC,SAAS,EAAE,CAAC,gCAAgC;CAC7C,CAAC,GACWhF,qBAAqB,CAmLjC;SAnLYA,qBAAqB", "names": ["Component", "EventEmitter", "moment", "coreConfig", "AppConfig", "ScheduleViewComponent", "constructor", "_http", "_tourService", "_seasonService", "_loadingService", "_commonsService", "_translateService", "onSelectTab", "activeIds", "selectedTabIndex", "tabs", "matches", "seasons", "teams", "tournaments", "params", "season_id", "tournament_id", "hasSeason", "ngOnInit", "getSeasons", "ngAfterViewInit", "ngOnDestroy", "getCurrentSeason", "to<PERSON>romise", "then", "res", "length", "dismiss", "id", "getTournamentOptions", "subscribe", "selectedTournament", "find", "tournament", "onSelectTournament", "getMatchesWithQuery", "today", "format", "numberOfMatches", "Object", "keys", "count", "key", "index", "label", "date", "push", "value", "nearestDate", "reduce", "a", "b", "Math", "abs", "diff", "findIndex", "tab", "sort", "setTimeout", "emit", "error", "console", "selectedTab", "element", "document", "getElementById", "scrollIntoView", "behavior", "block", "onSelectMatch", "match", "log", "onTournamentChange", "tournamentId", "t", "__decorate", "selector", "templateUrl", "styleUrls"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactivevn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\league-reports\\schedule-view\\schedule-view.component.ts"], "sourcesContent": ["import {\n  Component,\n  EventEmitter,\n  OnInit,\n  AfterViewInit,\n  OnD<PERSON>roy\n} from '@angular/core';\nimport { SeasonService } from 'app/services/season.service';\nimport { TournamentService } from 'app/services/tournament.service';\nimport moment from 'moment';\nimport { LoadingService } from 'app/services/loading.service';\nimport { coreConfig } from '../../../../app-config';\nimport { CommonsService } from 'app/services/commons.service';\nimport { AppConfig } from 'app/app-config';\nimport { TranslateService } from '@ngx-translate/core';\n\n@Component({\n  selector: 'app-schedule-view',\n  templateUrl: './schedule-view.component.html',\n  styleUrls: ['./schedule-view.component.scss']\n})\nexport class ScheduleViewComponent implements OnInit, AfterViewInit, OnDestroy {\n  onSelectTab = new EventEmitter();\n  activeIds = '';\n  selectedTabIndex = 0;\n  tabs = [];\n  matches: any = {};\n  tab: any;\n  selectedTeam: any;\n  selectedTournament: any;\n  seasons = [];\n  teams = [];\n  tournaments = [];\n  coreConfig = coreConfig;\n  params = { season_id: null, tournament_id: null };\n  hasSeason = true;\n  AppConfig = AppConfig;\n\n  constructor(\n    private _http: HttpClient,\n    public _tourService: TournamentService,\n    public _seasonService: SeasonService,\n    public _loadingService: LoadingService,\n    public _commonsService: CommonsService,\n    public _translateService: TranslateService\n  ) {\n    // Initialize with default values - don't use router to avoid conflicts\n    this.params.tournament_id = null;\n    this.params.season_id = null;\n  }\n\n  ngOnInit(): void {\n    this.getSeasons();\n  }\n\n  ngAfterViewInit(): void {\n    // Implementation for after view init\n  }\n\n  ngOnDestroy(): void {\n    // Cleanup if needed\n  }\n\n  getSeasons() {\n    this._seasonService\n      .getCurrentSeason('matches')\n      .toPromise()\n      .then((res) => {\n        this.seasons = res;\n        if (this.seasons.length == 0) {\n          this._loadingService.dismiss();\n          this.hasSeason = false;\n          return;\n        }\n        this.params.season_id = this.params.season_id\n          ? this.params.season_id\n          : res[0].id;\n        this.getTournamentOptions(this.params.season_id);\n      });\n  }\n\n  getTournamentOptions(season_id: any) {\n    this._seasonService\n      .getTournamentOptions(season_id, 0, true)\n      .subscribe((res) => {\n        this.tournaments = res.tournaments;\n        if (this.tournaments.length == 0) {\n          this.tabs = [];\n        }\n\n        this.params.tournament_id =\n          this.params.tournament_id || res.tournaments[0]?.id || null;\n        this.selectedTournament = this.tournaments.find(\n          (tournament) => tournament.id === this.params.tournament_id\n        );\n        this.onSelectTournament(this.selectedTournament);\n      });\n  }\n\n  onSelectTournament(tournament: any) {\n    this.selectedTournament = tournament;\n    this.params.tournament_id = tournament?.id;\n    // Remove router navigation to avoid conflicts with Table View\n    this.getMatchesWithQuery();\n  }\n\n  getMatchesWithQuery() {\n    this._tourService\n      .getMatchesWithQuery(\n        this.params.season_id,\n        `?tournament_id=${this.selectedTournament?.id}`\n      )\n      .subscribe(\n        (res) => {\n          let today = moment().format('YYYY-MM-DD');\n          this.matches = res.matches;\n          let numberOfMatches = Object.keys(this.matches).length;\n          let tabs = [];\n          if (numberOfMatches == 0) {\n            this.tabs = tabs;\n            return;\n          }\n          let count = 0;\n          this.activeIds = '';\n          for (let key in this.matches) {\n            if (key) {\n              this.activeIds += `match-panel-${count},`;\n            }\n            count++;\n            let index = tabs.length;\n            let label = 'TBD';\n            let date = 'TBD';\n            if (key) {\n              label = moment(key).format('ddd DD MMM');\n              date = moment(key).format('YYYY-MM-DD');\n            }\n\n            if (date == today) {\n              label = 'Today';\n            }\n            if (label != 'TBD') {\n              tabs.push({\n                label: label,\n                value: date,\n                index: index\n              });\n            }\n          }\n\n          if (this.matches) {\n            // select tab has date nearest to today\n            let nearestDate = Object.keys(this.matches).reduce((a, b) => {\n              return Math.abs(moment(a).diff(today, 'days')) <\n                Math.abs(moment(b).diff(today, 'days'))\n                ? a\n                : b;\n            });\n\n            this.selectedTabIndex = tabs.findIndex(\n              (tab) => tab.value === moment(nearestDate).format('YYYY-MM-DD')\n            );\n\n            // sort tabs by date\n            tabs.sort((a, b) => {\n              return moment(a.value).diff(moment(b.value));\n            });\n            this.tabs = tabs;\n            setTimeout(() => {\n              this.onSelectTab.emit(this.selectedTabIndex);\n            }, 500);\n          }\n        },\n        (error) => {\n          console.error('Error loading matches:', error);\n        },\n        () => {\n          this._loadingService.dismiss();\n        }\n      );\n  }\n\n  selectedTab(tab) {\n    this.tab = tab;\n    let element = document.getElementById(tab.value);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth', block: 'start' });\n    }\n  }\n\n  onSelectMatch(match) {\n    // Handle match selection if needed\n    console.log('Selected match:', match);\n  }\n\n\n\n  onTournamentChange(tournamentId: any) {\n    const tournament = this.tournaments.find(t => t.id === tournamentId);\n    this.onSelectTournament(tournament);\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}